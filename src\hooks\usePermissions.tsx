import React from 'react'
import { useAuthStore } from '../store/authStore'

export interface PermissionCheck {
  canManageBooks: boolean
  canManageMembers: boolean
  canManageBorrowings: boolean
  canManageReports: boolean
  canManageUsers: boolean
  canManageSystem: boolean
  canViewLogs: boolean
  canImportData: boolean
  canExportData: boolean
  canManageCategories: boolean
  canManageSources: boolean
}

export const usePermissions = (): PermissionCheck => {
  const { user } = useAuthStore()

  if (!user) {
    return {
      canManageBooks: false,
      canManageMembers: false,
      canManageBorrowings: false,
      canManageReports: false,
      canManageUsers: false,
      canManageSystem: false,
      canViewLogs: false,
      canImportData: false,
      canExportData: false,
      canManageCategories: false,
      canManageSources: false,
    }
  }

  return {
    canManageBooks: user.canManageBooks,
    canManageMembers: user.canManageMembers,
    canManageBorrowings: user.canManageBorrowings,
    canManageReports: user.canManageReports,
    canManageUsers: user.canManageUsers,
    canManageSystem: user.canManageSystem,
    canViewLogs: user.canViewLogs,
    canImportData: user.canImportData,
    canExportData: user.canExportData,
    canManageCategories: user.canManageCategories,
    canManageSources: user.canManageSources,
  }
}

// Helper functions for specific permission checks
export const useCanManageBooks = () => {
  const permissions = usePermissions()
  return permissions.canManageBooks
}

export const useCanManageMembers = () => {
  const permissions = usePermissions()
  return permissions.canManageMembers
}

export const useCanManageBorrowings = () => {
  const permissions = usePermissions()
  return permissions.canManageBorrowings
}

export const useCanManageReports = () => {
  const permissions = usePermissions()
  return permissions.canManageReports
}

export const useCanManageUsers = () => {
  const permissions = usePermissions()
  return permissions.canManageUsers
}

export const useCanManageSystem = () => {
  const permissions = usePermissions()
  return permissions.canManageSystem
}

export const useCanViewLogs = () => {
  const permissions = usePermissions()
  return permissions.canViewLogs
}

export const useCanImportData = () => {
  const permissions = usePermissions()
  return permissions.canImportData
}

export const useCanExportData = () => {
  const permissions = usePermissions()
  return permissions.canExportData
}

export const useCanManageCategories = () => {
  const permissions = usePermissions()
  return permissions.canManageCategories
}

export const useCanManageSources = () => {
  const permissions = usePermissions()
  return permissions.canManageSources
}

// Permission-based component wrapper
export const withPermission = (
  Component: React.ComponentType<any>,
  requiredPermission: keyof PermissionCheck,
  fallback?: React.ComponentType<any>
) => {
  return (props: any) => {
    const permissions = usePermissions()
    const hasPermission = permissions[requiredPermission]

    if (!hasPermission) {
      if (fallback) {
        const FallbackComponent = fallback
        return <FallbackComponent {...props} />
      }
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="text-gray-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m9-7a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900">Access Denied</h3>
            <p className="text-sm text-gray-500">
              You don't have permission to access this feature.
            </p>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

// Role-based component wrapper
export const withRole = (
  Component: React.ComponentType<any>,
  requiredRole: string,
  fallback?: React.ComponentType<any>
) => {
  return (props: any) => {
    const { user } = useAuthStore()
    const hasRole = user?.role === requiredRole

    if (!hasRole) {
      if (fallback) {
        const FallbackComponent = fallback
        return <FallbackComponent {...props} />
      }
      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="text-gray-400 mb-2">
              <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m9-7a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-sm font-medium text-gray-900">Admin Access Required</h3>
            <p className="text-sm text-gray-500">
              This feature is only available to administrators.
            </p>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}
