@echo off
title Mai<PERSON><PERSON> - PRODUCTION MODE
color 0A

echo.
echo ========================================
echo   Mai<PERSON><PERSON> Vika<PERSON> Library
echo   PRODUCTION MODE
echo ========================================
echo.
echo Starting library management system in PRODUCTION mode...
echo Memory: 1024MB, GC enabled, Production optimizations active
echo.

cd /d "%~dp0"

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies if node_modules doesn't exist
if not exist "node_modules" (
    echo Installing dependencies...
    npm install
    if errorlevel 1 (
        echo ERROR: Failed to install dependencies
        pause
        exit /b 1
    )
)

REM Ensure Prisma client is generated
if not exist "node_modules\.prisma\client" (
    echo Generating Prisma client...
    npm run db:generate
    if errorlevel 1 (
        echo ERROR: Failed to generate Prisma client
        echo Try running as Administrator or restart your computer
        pause
        exit /b 1
    )
)

REM Clean up any existing processes
echo Cleaning up existing processes...
taskkill /F /IM node.exe >nul 2>&1
taskkill /F /IM electron.exe >nul 2>&1

REM Build the application for production
echo Building application for production...
npm run build:prod
if errorlevel 1 (
    echo ERROR: Failed to build application
    pause
    exit /b 1
)

echo Starting application in PRODUCTION mode...
echo.
echo IMPORTANT: Keep this window open while using the application
echo Press Ctrl+C to stop the application
echo.

REM Start the application in production mode
npm run server:prod
