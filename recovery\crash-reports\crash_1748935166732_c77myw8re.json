{"id": "crash_1748935166732_c77myw8re", "timestamp": "2025-06-03T07:19:26.732Z", "error": {"name": "Error", "message": "listen EADDRINUSE: address already in use :::3002", "stack": "Error: listen EADDRINUSE: address already in use :::3002\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at Server.listen (node:net:2101:7)\n    at Function.listen (D:\\libraryFinal\\library\\node_modules\\express\\lib\\application.js:635:24)\n    at Object.<anonymous> (D:\\libraryFinal\\library\\server\\index.js:418:5)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)", "code": "EADDRINUSE"}, "context": {"type": "uncaughtException", "origin": "uncaughtException", "severity": "critical"}, "system": {"platform": "win32", "arch": "x64", "nodeVersion": "v22.16.0", "pid": 16788, "uptime": 1.619261, "memoryUsage": {"rss": 152285184, "heapTotal": 112467968, "heapUsed": 68969880, "external": 4502845, "arrayBuffers": 858716}, "loadAverage": null}, "environment": {"nodeEnv": "development", "cwd": "D:\\libraryFinal\\library", "argv": ["C:\\Program Files\\nodejs\\node.exe", "D:\\libraryFinal\\library\\server\\index.js"]}}