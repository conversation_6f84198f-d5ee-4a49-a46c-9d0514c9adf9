const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { extractUser, requireAdmin } = require('../middleware/auth');
const { logAction, ACTIONS } = require('../utils/logger');
const router = express.Router();
const prisma = new PrismaClient();

// Get all soft-deleted items across all entities
router.get('/soft-deleted', extractUser, requireAdmin, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      entity = '',
      search = '',
      sortBy = 'updatedAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const take = parseInt(limit);

    // Define all entities that support soft delete
    const entities = [
      'author', 'category', 'language', 'source', 'book', 
      'member', 'membershipRenewal', 'borrowing', 'publisher', 
      'bookSeries', 'subject', 'location'
    ];

    const results = {};
    let totalCount = 0;

    // If specific entity is requested
    if (entity && entities.includes(entity)) {
      const entityData = await getSoftDeletedForEntity(entity, { skip, take, search, sortBy, sortOrder });
      results[entity] = entityData.items;
      totalCount = entityData.total;
    } else {
      // Get soft-deleted items from all entities
      for (const entityName of entities) {
        try {
          const entityData = await getSoftDeletedForEntity(entityName, { 
            skip: 0, 
            take: entity ? take : 10, // Limit per entity when showing all
            search, 
            sortBy, 
            sortOrder 
          });
          results[entityName] = entityData.items;
          totalCount += entityData.total;
        } catch (error) {
          console.error(`Error fetching ${entityName}:`, error);
          results[entityName] = [];
        }
      }
    }

    res.json({
      success: true,
      data: results,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      },
      entities: entities.map(e => ({
        name: e,
        count: results[e]?.length || 0
      }))
    });

  } catch (error) {
    console.error('Error fetching soft-deleted items:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to fetch soft-deleted items',
      details: error.message 
    });
  }
});

// Helper function to get soft-deleted items for a specific entity
async function getSoftDeletedForEntity(entityName, options = {}) {
  const { skip = 0, take = 10, search = '', sortBy = 'updatedAt', sortOrder = 'desc' } = options;

  const orderBy = { [sortBy]: sortOrder };

  // Helper function to get deletion info from UserLog
  const getDeletionInfo = async (entityType, items) => {
    if (!items || items.length === 0) return items;

    const entityIds = items.map(item => item.id);

    // Get deletion logs for these items
    const deletionLogs = await prisma.userLog.findMany({
      where: {
        entity: entityType.toUpperCase(),
        entityId: { in: entityIds },
        action: 'DELETE'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Create a map of entityId to deletion info
    const deletionMap = {};
    deletionLogs.forEach(log => {
      if (!deletionMap[log.entityId]) {
        deletionMap[log.entityId] = {
          deletedBy: log.user,
          deletedAt: log.createdAt,
          deletionDetails: log.details
        };
      }
    });

    // Add deletion info to items
    return items.map(item => ({
      ...item,
      deletionInfo: deletionMap[item.id] || null
    }));
  };
  
  switch (entityName) {
    case 'author':
      const authorWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } }
          ]
        })
      };
      const [authors, authorCount] = await Promise.all([
        prisma.author.findMany({
          where: authorWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.author.count({ where: authorWhere })
      ]);
      const authorsWithDeletionInfo = await getDeletionInfo('AUTHOR', authors);
      return { items: authorsWithDeletionInfo, total: authorCount };

    case 'category':
      const categoryWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } }
          ]
        })
      };
      const [categories, categoryCount] = await Promise.all([
        prisma.category.findMany({
          where: categoryWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.category.count({ where: categoryWhere })
      ]);
      const categoriesWithDeletionInfo = await getDeletionInfo('CATEGORY', categories);
      return { items: categoriesWithDeletionInfo, total: categoryCount };

    case 'language':
      const languageWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } },
            { code: { contains: search } }
          ]
        })
      };
      const [languages, languageCount] = await Promise.all([
        prisma.language.findMany({
          where: languageWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.language.count({ where: languageWhere })
      ]);
      const languagesWithDeletionInfo = await getDeletionInfo('LANGUAGE', languages);
      return { items: languagesWithDeletionInfo, total: languageCount };

    case 'source':
      const sourceWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } },
            { contactPerson: { contains: search } }
          ]
        })
      };
      const [sources, sourceCount] = await Promise.all([
        prisma.source.findMany({
          where: sourceWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.source.count({ where: sourceWhere })
      ]);
      const sourcesWithDeletionInfo = await getDeletionInfo('SOURCE', sources);
      return { items: sourcesWithDeletionInfo, total: sourceCount };

    case 'book':
      const bookWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { title: { contains: search } },
            { titleNepali: { contains: search } },
            { accessionNo: { contains: search } },
            { isbn: { contains: search } }
          ]
        })
      };
      const [books, bookCount] = await Promise.all([
        prisma.book.findMany({
          where: bookWhere,
          orderBy,
          skip,
          take,
          include: {
            author: { select: { name: true, nameNepali: true } },
            category: { select: { name: true, nameNepali: true } },
            publisher_rel: { select: { name: true } },
            language: { select: { name: true, nameNepali: true } }
          }
        }),
        prisma.book.count({ where: bookWhere })
      ]);
      const booksWithDeletionInfo = await getDeletionInfo('BOOK', books);
      return { items: booksWithDeletionInfo, total: bookCount };

    case 'member':
      const memberWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } },
            { email: { contains: search } },
            { phone: { contains: search } },
            { memberNo: { contains: search } }
          ]
        })
      };
      const [members, memberCount] = await Promise.all([
        prisma.member.findMany({
          where: memberWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { borrowings: true } }
          }
        }),
        prisma.member.count({ where: memberWhere })
      ]);
      const membersWithDeletionInfo = await getDeletionInfo('MEMBER', members);
      return { items: membersWithDeletionInfo, total: memberCount };

    case 'publisher':
      const publisherWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } },
            { address: { contains: search } }
          ]
        })
      };
      const [publishers, publisherCount] = await Promise.all([
        prisma.publisher.findMany({
          where: publisherWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.publisher.count({ where: publisherWhere })
      ]);
      const publishersWithDeletionInfo = await getDeletionInfo('PUBLISHER', publishers);
      return { items: publishersWithDeletionInfo, total: publisherCount };

    case 'borrowing':
      const borrowingWhere = {
        isDeleted: true,
        ...(search && {
          book: {
            OR: [
              { title: { contains: search } },
              { accessionNo: { contains: search } }
            ]
          }
        })
      };
      const [borrowings, borrowingCount] = await Promise.all([
        prisma.borrowing.findMany({
          where: borrowingWhere,
          orderBy,
          skip,
          take,
          include: {
            book: { select: { title: true, accessionNo: true } },
            member: { select: { name: true, memberNo: true } }
          }
        }),
        prisma.borrowing.count({ where: borrowingWhere })
      ]);
      const borrowingsWithDeletionInfo = await getDeletionInfo('BORROWING', borrowings);
      return { items: borrowingsWithDeletionInfo, total: borrowingCount };

    case 'bookSeries':
      const bookSeriesWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } }
          ]
        })
      };
      const [bookSeries, bookSeriesCount] = await Promise.all([
        prisma.bookSeries.findMany({
          where: bookSeriesWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.bookSeries.count({ where: bookSeriesWhere })
      ]);
      const bookSeriesWithDeletionInfo = await getDeletionInfo('BOOKSERIES', bookSeries);
      return { items: bookSeriesWithDeletionInfo, total: bookSeriesCount };

    case 'subject':
      const subjectWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { name: { contains: search } },
            { nameNepali: { contains: search } },
            { code: { contains: search } }
          ]
        })
      };
      const [subjects, subjectCount] = await Promise.all([
        prisma.subject.findMany({
          where: subjectWhere,
          orderBy,
          skip,
          take,
          include: {
            category: { select: { name: true } },
            _count: { select: { books: true } }
          }
        }),
        prisma.subject.count({ where: subjectWhere })
      ]);
      const subjectsWithDeletionInfo = await getDeletionInfo('SUBJECT', subjects);
      return { items: subjectsWithDeletionInfo, total: subjectCount };

    case 'location':
      const locationWhere = {
        isDeleted: true,
        ...(search && {
          OR: [
            { shelf: { contains: search } },
            { row: { contains: search } },
            { description: { contains: search } }
          ]
        })
      };
      const [locations, locationCount] = await Promise.all([
        prisma.location.findMany({
          where: locationWhere,
          orderBy,
          skip,
          take,
          include: {
            _count: { select: { books: true } }
          }
        }),
        prisma.location.count({ where: locationWhere })
      ]);
      const locationsWithDeletionInfo = await getDeletionInfo('LOCATION', locations);
      return { items: locationsWithDeletionInfo, total: locationCount };

    case 'membershipRenewal':
      const renewalWhere = {
        isDeleted: true,
        ...(search && {
          member: {
            OR: [
              { name: { contains: search } },
              { memberNo: { contains: search } }
            ]
          }
        })
      };
      const [renewals, renewalCount] = await Promise.all([
        prisma.membershipRenewal.findMany({
          where: renewalWhere,
          orderBy,
          skip,
          take,
          include: {
            member: { select: { name: true, memberNo: true } }
          }
        }),
        prisma.membershipRenewal.count({ where: renewalWhere })
      ]);
      const renewalsWithDeletionInfo = await getDeletionInfo('MEMBERSHIPRENEWAL', renewals);
      return { items: renewalsWithDeletionInfo, total: renewalCount };

    default:
      return { items: [], total: 0 };
  }
}

// Restore a soft-deleted item
router.post('/restore/:entity/:id', extractUser, requireAdmin, async (req, res) => {
  try {
    const { entity, id } = req.params;
    const { reason = 'Admin restoration' } = req.body;

    // Validate entity
    const validEntities = [
      'author', 'category', 'language', 'source', 'book',
      'member', 'membershipRenewal', 'borrowing', 'publisher',
      'bookSeries', 'subject', 'location'
    ];

    if (!validEntities.includes(entity)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid entity type'
      });
    }

    let restoredItem;
    let entityDisplayName;

    // Restore the item based on entity type
    switch (entity) {
      case 'author':
        restoredItem = await prisma.author.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'category':
        restoredItem = await prisma.category.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'language':
        restoredItem = await prisma.language.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'source':
        restoredItem = await prisma.source.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'book':
        restoredItem = await prisma.book.update({
          where: { id },
          data: { isDeleted: false, isAvailable: true }
        });
        entityDisplayName = restoredItem.title;
        break;

      case 'member':
        restoredItem = await prisma.member.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'publisher':
        restoredItem = await prisma.publisher.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'borrowing':
        restoredItem = await prisma.borrowing.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = `Borrowing ${id}`;
        break;

      case 'bookSeries':
        restoredItem = await prisma.bookSeries.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'subject':
        restoredItem = await prisma.subject.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = restoredItem.name;
        break;

      case 'location':
        restoredItem = await prisma.location.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = `Shelf ${restoredItem.shelf}${restoredItem.row ? ` - Row ${restoredItem.row}` : ''}`;
        break;

      case 'membershipRenewal':
        restoredItem = await prisma.membershipRenewal.update({
          where: { id },
          data: { isDeleted: false }
        });
        entityDisplayName = `Renewal ${id}`;
        break;

      default:
        return res.status(400).json({
          success: false,
          error: 'Unsupported entity type'
        });
    }

    // Log the restoration action
    await logAction(req, ACTIONS.UPDATE, entity.toUpperCase(), id,
      `Restored ${entity}: ${entityDisplayName}. Reason: ${reason}`);

    res.json({
      success: true,
      message: `${entity.charAt(0).toUpperCase() + entity.slice(1)} restored successfully`,
      data: restoredItem
    });

  } catch (error) {
    console.error('Error restoring item:', error);

    // Handle specific errors
    if (error.code === 'P2025') {
      return res.status(404).json({
        success: false,
        error: 'Item not found or already restored'
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to restore item',
      details: error.message
    });
  }
});

// Get statistics for soft-deleted items
router.get('/soft-deleted/stats', extractUser, requireAdmin, async (req, res) => {
  try {
    const stats = {};

    const entities = [
      'author', 'category', 'language', 'source', 'book',
      'member', 'membershipRenewal', 'borrowing', 'publisher',
      'bookSeries', 'subject', 'location'
    ];

    for (const entity of entities) {
      try {
        const count = await getSoftDeletedCount(entity);
        stats[entity] = count;
      } catch (error) {
        console.error(`Error getting count for ${entity}:`, error);
        stats[entity] = 0;
      }
    }

    const totalDeleted = Object.values(stats).reduce((sum, count) => sum + count, 0);

    res.json({
      success: true,
      stats,
      totalDeleted
    });

  } catch (error) {
    console.error('Error fetching soft-deleted stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch statistics'
    });
  }
});

// Helper function to get count of soft-deleted items for an entity
async function getSoftDeletedCount(entityName) {
  switch (entityName) {
    case 'author':
      return await prisma.author.count({ where: { isDeleted: true } });
    case 'category':
      return await prisma.category.count({ where: { isDeleted: true } });
    case 'language':
      return await prisma.language.count({ where: { isDeleted: true } });
    case 'source':
      return await prisma.source.count({ where: { isDeleted: true } });
    case 'book':
      return await prisma.book.count({ where: { isDeleted: true } });
    case 'member':
      return await prisma.member.count({ where: { isDeleted: true } });
    case 'publisher':
      return await prisma.publisher.count({ where: { isDeleted: true } });
    case 'borrowing':
      return await prisma.borrowing.count({ where: { isDeleted: true } });
    case 'bookSeries':
      return await prisma.bookSeries.count({ where: { isDeleted: true } });
    case 'subject':
      return await prisma.subject.count({ where: { isDeleted: true } });
    case 'location':
      return await prisma.location.count({ where: { isDeleted: true } });
    case 'membershipRenewal':
      return await prisma.membershipRenewal.count({ where: { isDeleted: true } });
    default:
      return 0;
  }
}

module.exports = router;
