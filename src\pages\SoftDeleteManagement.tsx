import React, { useState, useEffect } from 'react'
import { 
  TrashIcon, 
  ArrowPathIcon, 
  MagnifyingGlassIcon,
  FunnelIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'

interface SoftDeletedItem {
  id: string
  name?: string
  nameNepali?: string
  title?: string
  titleNepali?: string
  accessionNo?: string
  memberNo?: string
  updatedAt: string
  createdAt: string
  deletionInfo?: {
    deletedBy: {
      id: string
      name: string
      username: string
    }
    deletedAt: string
    deletionDetails?: string
  }
  [key: string]: any
}

interface EntityStats {
  [key: string]: number
}

interface SoftDeletedData {
  [entity: string]: SoftDeletedItem[]
}

const SoftDeleteManagement: React.FC = () => {
  const [loading, setLoading] = useState(true)
  const [restoring, setRestoring] = useState<string | null>(null)
  const [data, setData] = useState<SoftDeletedData>({})
  const [stats, setStats] = useState<EntityStats>({})
  const [totalDeleted, setTotalDeleted] = useState(0)
  const [selectedEntity, setSelectedEntity] = useState<string>('')
  const [searchTerm, setSearchTerm] = useState('')
  const [showRestoreModal, setShowRestoreModal] = useState(false)
  const [itemToRestore, setItemToRestore] = useState<{entity: string, item: SoftDeletedItem} | null>(null)
  const [restoreReason, setRestoreReason] = useState('')

  const entityLabels: { [key: string]: string } = {
    author: 'Authors',
    category: 'Categories', 
    language: 'Languages',
    source: 'Sources',
    book: 'Books',
    member: 'Members',
    publisher: 'Publishers',
    borrowing: 'Borrowings',
    bookSeries: 'Book Series',
    subject: 'Subjects',
    location: 'Locations',
    membershipRenewal: 'Membership Renewals'
  }

  const entityLabelsNepali: { [key: string]: string } = {
    author: 'लेखकहरू',
    category: 'श्रेणीहरू',
    language: 'भाषाहरू', 
    source: 'स्रोतहरू',
    book: 'पुस्तकहरू',
    member: 'सदस्यहरू',
    publisher: 'प्रकाशकहरू',
    borrowing: 'जारी',
    bookSeries: 'पुस्तक श्रृंखला',
    subject: 'विषयहरू',
    location: 'स्थानहरू',
    membershipRenewal: 'सदस्यता नवीकरण'
  }

  useEffect(() => {
    fetchData()
    fetchStats()
  }, [selectedEntity, searchTerm])

  const fetchData = async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: '1',
        limit: '50',
        ...(selectedEntity && { entity: selectedEntity }),
        ...(searchTerm && { search: searchTerm })
      })

      const response = await fetch(`/api/admin/soft-deleted?${params}`)
      if (response.ok) {
        const result = await response.json()
        setData(result.data)
      }
    } catch (error) {
      console.error('Error fetching soft-deleted data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/soft-deleted/stats')
      if (response.ok) {
        const result = await response.json()
        setStats(result.stats)
        setTotalDeleted(result.totalDeleted)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleRestore = async () => {
    if (!itemToRestore) return

    setRestoring(itemToRestore.item.id)
    try {
      const response = await fetch(`/api/admin/restore/${itemToRestore.entity}/${itemToRestore.item.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason: restoreReason })
      })

      if (response.ok) {
        // Refresh data
        await fetchData()
        await fetchStats()
        setShowRestoreModal(false)
        setItemToRestore(null)
        setRestoreReason('')
      } else {
        const error = await response.json()
        alert(`Error: ${error.error}`)
      }
    } catch (error) {
      console.error('Error restoring item:', error)
      alert('Failed to restore item')
    } finally {
      setRestoring(null)
    }
  }

  const openRestoreModal = (entity: string, item: SoftDeletedItem) => {
    setItemToRestore({ entity, item })
    setShowRestoreModal(true)
  }

  const getDisplayName = (entity: string, item: SoftDeletedItem): string => {
    if (entity === 'book') {
      return item.title || item.titleNepali || 'Unknown Book'
    }
    if (entity === 'member') {
      return `${item.name || item.nameNepali || 'Unknown'} (${item.memberNo || 'No ID'})`
    }
    if (entity === 'borrowing') {
      return `${item.book?.title || 'Unknown Book'} - ${item.member?.name || 'Unknown Member'}`
    }
    if (entity === 'location') {
      return `Shelf ${item.shelf || 'Unknown'}${item.row ? ` - Row ${item.row}` : ''}`
    }
    return item.name || item.nameNepali || item.title || item.titleNepali || 'Unknown'
  }

  const getSecondaryInfo = (entity: string, item: SoftDeletedItem): string => {
    if (entity === 'book') {
      return `${item.author?.name || 'Unknown Author'} | ${item.accessionNo || 'No Acc. No.'}`
    }
    if (entity === 'member') {
      return item.email || item.phone || 'No contact info'
    }
    if (entity === 'borrowing') {
      return `Issued: ${new Date(item.issueDate).toLocaleDateString()}`
    }
    if (entity === 'location') {
      return item.description || 'No description'
    }
    return item.nameNepali || item.titleNepali || ''
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner spinner-lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Soft Delete Management
            <span className="text-sm font-normal text-gray-500 ml-2">
              सफ्ट डिलिट व्यवस्थापन
            </span>
          </h1>
          <p className="text-gray-600">
            Restore deleted items | मेटाइएका वस्तुहरू पुनर्स्थापना गर्नुहोस्
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <div className="bg-red-50 border border-red-200 rounded-lg p-3">
            <div className="flex items-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-sm font-medium text-red-800">
                {totalDeleted} items deleted | {totalDeleted} वस्तुहरू मेटाइएका
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
        {Object.entries(stats).map(([entity, count]) => (
          <div
            key={entity}
            className={`bg-white border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedEntity === entity ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => setSelectedEntity(selectedEntity === entity ? '' : entity)}
          >
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{count}</div>
              <div className="text-sm font-medium text-gray-700">{entityLabels[entity]}</div>
              <div className="text-xs text-gray-500">{entityLabelsNepali[entity]}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Filters */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search deleted items..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          <div className="sm:w-48">
            <select
              value={selectedEntity}
              onChange={(e) => setSelectedEntity(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Entities</option>
              {Object.entries(entityLabels).map(([key, label]) => (
                <option key={key} value={key}>{label}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="space-y-6">
        {Object.entries(data).map(([entity, items]) => {
          if (!items || items.length === 0) return null
          
          return (
            <div key={entity} className="bg-white border border-gray-200 rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">
                  {entityLabels[entity]} ({items.length})
                  <span className="text-sm font-normal text-gray-500 ml-2">
                    {entityLabelsNepali[entity]}
                  </span>
                </h3>
              </div>
              <div className="divide-y divide-gray-200">
                {items.map((item) => (
                  <div key={item.id} className="px-6 py-4 flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <TrashIcon className="w-5 h-5 text-red-500" />
                        <div>
                          <h4 className="text-sm font-medium text-gray-900">
                            {getDisplayName(entity, item)}
                          </h4>
                          <p className="text-sm text-gray-500">
                            {getSecondaryInfo(entity, item)}
                          </p>
                          <div className="text-xs text-gray-400 space-y-1">
                            <p>
                              Deleted: {item.deletionInfo?.deletedAt ?
                                new Date(item.deletionInfo.deletedAt).toLocaleString() :
                                new Date(item.updatedAt).toLocaleString()
                              }
                            </p>
                            {item.deletionInfo?.deletedBy && (
                              <p>
                                By: {item.deletionInfo.deletedBy.name} ({item.deletionInfo.deletedBy.username})
                              </p>
                            )}
                            {item.deletionInfo?.deletionDetails && (
                              <p className="text-gray-500">
                                {item.deletionInfo.deletionDetails}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => openRestoreModal(entity, item)}
                      disabled={restoring === item.id}
                      className="btn btn-sm btn-primary"
                    >
                      {restoring === item.id ? (
                        <>
                          <div className="spinner spinner-sm mr-2" />
                          Restoring...
                        </>
                      ) : (
                        <>
                          <ArrowPathIcon className="w-4 h-4 mr-2" />
                          Restore
                        </>
                      )}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* No Results */}
      {Object.values(data).every(items => !items || items.length === 0) && (
        <div className="text-center py-12">
          <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No deleted items found</h3>
          <p className="text-gray-500">
            {selectedEntity ? `No deleted ${entityLabels[selectedEntity].toLowerCase()} found` : 'All items are active'}
          </p>
        </div>
      )}

      {/* Restore Modal */}
      {showRestoreModal && itemToRestore && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center mb-4">
              <InformationCircleIcon className="w-6 h-6 text-blue-500 mr-3" />
              <h3 className="text-lg font-semibold">Confirm Restoration</h3>
            </div>
            
            <div className="mb-4">
              <p className="text-gray-700 mb-2">
                Are you sure you want to restore this {itemToRestore.entity}?
              </p>
              <div className="bg-gray-50 p-3 rounded border">
                <p className="font-medium">{getDisplayName(itemToRestore.entity, itemToRestore.item)}</p>
                <p className="text-sm text-gray-600">{getSecondaryInfo(itemToRestore.entity, itemToRestore.item)}</p>
                {itemToRestore.item.deletionInfo && (
                  <div className="mt-2 pt-2 border-t border-gray-200">
                    <p className="text-xs text-gray-500">
                      Deleted: {new Date(itemToRestore.item.deletionInfo.deletedAt).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500">
                      By: {itemToRestore.item.deletionInfo.deletedBy.name} ({itemToRestore.item.deletionInfo.deletedBy.username})
                    </p>
                    {itemToRestore.item.deletionInfo.deletionDetails && (
                      <p className="text-xs text-gray-600 mt-1">
                        {itemToRestore.item.deletionInfo.deletionDetails}
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason for restoration (optional):
              </label>
              <textarea
                value={restoreReason}
                onChange={(e) => setRestoreReason(e.target.value)}
                placeholder="Enter reason for restoring this item..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowRestoreModal(false)
                  setItemToRestore(null)
                  setRestoreReason('')
                }}
                className="btn btn-secondary"
              >
                Cancel
              </button>
              <button
                onClick={handleRestore}
                disabled={restoring !== null}
                className="btn btn-primary"
              >
                {restoring ? (
                  <>
                    <div className="spinner spinner-sm mr-2" />
                    Restoring...
                  </>
                ) : (
                  'Restore Item'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default SoftDeleteManagement
