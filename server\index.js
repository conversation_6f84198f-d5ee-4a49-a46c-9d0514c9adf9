// Load environment variables
require('dotenv').config();

// Load port configuration if available
const path = require('path');
const fs = require('fs');
const portsEnvPath = path.join(__dirname, '../.env.ports');
if (fs.existsSync(portsEnvPath)) {
  require('dotenv').config({ path: portsEnvPath });
}

// Initialize production memory configuration
const memoryConfig = require('../production-memory-config');

const express = require('express');
const cors = require('cors');
const session = require('express-session');
const { PrismaClient } = require('@prisma/client');
const FileSessionStore = require('./utils/fileSessionStore');

// Import error handling
const { errorHandler } = require('./utils/errorHandler');
const { globalErrorHandler: globalErrorHandlerInstance } = require('./utils/globalErrorHandler');
const CrashRecoveryService = require('./utils/crashRecovery');
const {
  globalErrorHandler,
  notFoundHandler,
  timeoutHandler,
  memoryMonitoringMiddleware,
  securityErrorHandler,
  setupGlobalErrorHandlers,
  gracefulShutdownHandler
} = require('./middleware/errorMiddleware');
const HealthCheckService = require('./utils/healthCheck');

const app = express();

// Initialize Prisma with production-ready configuration
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'production' ? ['error'] : ['query', 'info', 'warn', 'error'],
  errorFormat: 'pretty'
});

const PORT = process.env.PORT || 3002;
const isDev = process.env.NODE_ENV !== 'production';

// Initialize services
const healthCheck = new HealthCheckService();
const crashRecovery = new CrashRecoveryService();

// Setup global error handlers
setupGlobalErrorHandlers();
gracefulShutdownHandler();

// Check if recovery is needed on startup
async function checkStartupRecovery() {
  try {
    const recoveryInfo = await crashRecovery.checkRecoveryNeeded();

    if (recoveryInfo.needed) {
      console.log('🔧 Recovery needed on startup:', recoveryInfo.reasons);
      const recoveryResult = await crashRecovery.performRecovery(recoveryInfo);

      if (recoveryResult.success) {
        console.log('✅ Startup recovery completed successfully');
      } else {
        console.error('❌ Startup recovery failed:', recoveryResult.error);
      }
    } else {
      console.log('✅ No recovery needed on startup');
    }
  } catch (error) {
    console.error('Failed to check startup recovery:', error);
  }
}

// Security headers middleware
app.use((req, res, next) => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=(), payment=()');

  // Set CSP header for API responses
  const csp = [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self' 'unsafe-inline'",
    "font-src 'self' data:",
    "img-src 'self' data: blob:",
    "connect-src 'self'",
    "media-src 'self'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'"
  ].join('; ');

  res.setHeader('Content-Security-Policy', csp);
  next();
});

// Enhanced middleware with error handling
app.use(timeoutHandler(30000)); // 30 second timeout
app.use(memoryMonitoringMiddleware);
app.use(securityErrorHandler);
// Dynamic CORS configuration based on detected ports
const CLIENT_PORT = process.env.CLIENT_PORT || process.env.VITE_PORT || '3000';
app.use(cors({
  origin: [`http://localhost:${CLIENT_PORT}`, 'http://localhost:3001'],
  credentials: true
}));

// Session configuration with persistent file-based storage
const sessionConfig = {
  secret: process.env.SESSION_SECRET || 'maithili-library-secret-key-2025',
  resave: false,
  saveUninitialized: false,
  store: new FileSessionStore({
    dir: path.join(__dirname, '../sessions'),
    ttl: 7 * 24 * 60 * 60 * 1000, // 7 days for desktop app
    reapInterval: 60 * 60 * 1000 // Clean up every hour
  }),
  cookie: {
    secure: false, // Set to true in production with HTTPS
    httpOnly: true,
    maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days for desktop app
  },
  name: 'maithili.sid'
};

app.use(session(sessionConfig));

app.use(express.json({ limit: '10mb' })); // Add size limit
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Only serve static files in production
if (!isDev) {
  app.use(express.static(path.join(__dirname, '../build')));

  // Serve images from multiple possible locations
  app.use('/images', express.static(path.join(__dirname, '../public/images')));
  app.use('/images', express.static(path.join(__dirname, '../build/images')));

  // Only serve from resourcesPath if it exists (packaged app)
  if (process.resourcesPath) {
    app.use('/images', express.static(path.join(process.resourcesPath, 'images')));
  }

  // Serve assets
  app.use('/assets', express.static(path.join(__dirname, '../assets')));

  // Only serve from resourcesPath if it exists (packaged app)
  if (process.resourcesPath) {
    app.use('/assets', express.static(path.join(process.resourcesPath, 'assets')));
  }
}

// Import routes
const authRoutes = require('./routes/auth');
const bookRoutes = require('./routes/books');
const memberRoutes = require('./routes/members');
const membershipRenewalRoutes = require('./routes/membershipRenewal');
const borrowingRoutes = require('./routes/borrowings');
const scheduledEmailRoutes = require('./routes/scheduledEmails');
const authorRoutes = require('./routes/authors');
const categoryRoutes = require('./routes/categories');
const languageRoutes = require('./routes/languages');
const sourceRoutes = require('./routes/sources');
const publisherRoutes = require('./routes/publishers');
const bookSeriesRoutes = require('./routes/book-series');
const subjectRoutes = require('./routes/subjects');
const locationRoutes = require('./routes/locations');
const conditionRoutes = require('./routes/conditions');
const reportRoutes = require('./routes/reports');
const userRoutes = require('./routes/users');
const adminRoutes = require('./routes/admin');
const { router: userLogRoutes } = require('./routes/userLogs');
const pdfRoutes = require('./routes/pdf');
const importRoutes = require('./routes/import');
const emailRoutes = require('./routes/email');
const backupRoutes = require('./routes/backup');
const errorManagementRoutes = require('./routes/errorManagement');
const healthRoutes = require('./routes/health');
const autoRecoveryService = require('./services/autoRecoveryService');

// Use routes
app.use('/api/auth', authRoutes);
app.use('/api/books', bookRoutes);
app.use('/api/members', memberRoutes);
app.use('/api/membership-renewal', membershipRenewalRoutes);
app.use('/api/borrowings', borrowingRoutes);
app.use('/api/scheduled-emails', scheduledEmailRoutes);
app.use('/api/authors', authorRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/languages', languageRoutes);
app.use('/api/sources', sourceRoutes);
app.use('/api/publishers', publisherRoutes);
app.use('/api/book-series', bookSeriesRoutes);
app.use('/api/subjects', subjectRoutes);
app.use('/api/locations', locationRoutes);
app.use('/api/conditions', conditionRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/users', userRoutes);
app.use('/api/admin', adminRoutes);
app.use('/api/user-logs', userLogRoutes);
app.use('/api/pdf', pdfRoutes);
app.use('/api/import', importRoutes);
app.use('/api/email', emailRoutes);
app.use('/api/backup', backupRoutes);
app.use('/api/error-management', errorManagementRoutes);
app.use('/api/health', healthRoutes);

// Simple root endpoint for health checks (handles HEAD and GET requests)
app.all('/', (req, res) => {
  res.status(200).json({
    status: 'ok',
    service: 'Maithili Vikas Kosh Library API',
    timestamp: new Date().toISOString()
  });
});

// Enhanced health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const healthStatus = await healthCheck.runAllChecks();
    const systemInfo = healthCheck.getSystemInfo();

    res.status(healthStatus.status === 'healthy' ? 200 : 503).json({
      ...healthStatus,
      system: systemInfo
    });
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'health' });
  }
});

// Error reporting endpoint
app.post('/api/errors/report', async (req, res) => {
  try {
    const errorReport = req.body;

    // Log the frontend error
    await errorHandler.logErrorToFile(new Error(errorReport.error?.message || 'Frontend Error'), {
      type: 'frontend',
      ...errorReport
    });

    res.json({ success: true, message: 'Error reported successfully' });
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'error-report' });
  }
});

// Error feedback endpoint
app.post('/api/errors/feedback', async (req, res) => {
  try {
    const { errorId, feedback, timestamp } = req.body;

    if (!errorId || !feedback) {
      return res.status(400).json({
        success: false,
        error: 'Error ID and feedback are required'
      });
    }

    // Log the feedback
    await errorHandler.logErrorToFile(new Error('User Feedback'), {
      type: 'feedback',
      errorId,
      feedback,
      timestamp,
      userId: req.user?.id,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });

    res.json({
      success: true,
      message: 'Feedback submitted successfully',
      errorId
    });
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'error-feedback' });
  }
});

// Error statistics endpoint - enhanced to read from log files
app.get('/api/errors/stats', async (req, res) => {
  try {
    // Read error statistics from log files
    const stats = await errorHandler.getErrorStatistics();
    res.json({ success: true, stats });
  } catch (error) {
    // Fallback to empty stats if log reading fails
    const fallbackStats = {
      total: 0,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      resolved: 0,
      unresolved: 0,
      categories: {},
      recentTrends: []
    };
    res.json({ success: true, stats: fallbackStats });
  }
});

// Error export endpoint - export error data from log files
app.get('/api/errors/export', async (req, res) => {
  try {
    // Get error data for export
    const errors = await errorHandler.getErrorsForExport();

    res.json({
      success: true,
      errors,
      count: errors.length,
      exportedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Failed to export error data:', error);
    res.json({
      success: false,
      errors: [],
      count: 0,
      error: 'Failed to read error logs'
    });
  }
});

// Recovery management endpoints
app.get('/api/recovery/status', async (req, res) => {
  try {
    const recoveryInfo = await crashRecovery.checkRecoveryNeeded();
    const statistics = crashRecovery.getRecoveryStatistics();
    const globalStats = globalErrorHandlerInstance.getErrorStatistics();

    res.json({
      recovery: recoveryInfo,
      statistics,
      globalStats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'recovery-status' });
  }
});

app.post('/api/recovery/trigger', async (req, res) => {
  try {
    const result = await globalErrorHandlerInstance.triggerRecoveryCheck();
    res.json(result);
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'recovery-trigger' });
  }
});

app.get('/api/recovery/crashes', async (req, res) => {
  try {
    const timeWindow = parseInt(req.query.timeWindow) || 24 * 60 * 60 * 1000; // 24 hours default
    const recentCrashes = await crashRecovery.getRecentCrashes(timeWindow);

    res.json({
      crashes: recentCrashes,
      count: recentCrashes.length,
      timeWindow,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    await errorHandler.handleError(error, req, res, { endpoint: 'recovery-crashes' });
  }
});

// Serve React app for any non-API routes (only in production)
if (!isDev) {
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../build/index.html'));
  });
}

// 404 handler for unknown routes
app.use(notFoundHandler);

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('Shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down gracefully...');
  await prisma.$disconnect();
  process.exit(0);
});

// Initialize desktop email service (for desktop app deployment)
const desktopEmailService = require('./services/desktopEmailService');

// Initialize backup schedulers
const CloudBackupScheduler = require('../scripts/cloud-backup-scheduler');
const HybridBackupScheduler = require('../scripts/hybrid-backup-scheduler');
const EmailBackupScheduler = require('../scripts/email-backup-scheduler');

// Import user initialization
const { initializeProductionUsers } = require('./utils/userInitializer');
const ProductionDbInitializer = require('./utils/productionDbInitializer');

// Start server
app.listen(PORT, async () => {
  console.log(`🚀 Maithili Vikash Kosh Library API running on port ${PORT}`);
  console.log(`📚 Database connected successfully`);

  if (process.env.NODE_ENV === 'production') {
    console.log('🎯 Running in PRODUCTION mode');
  } else {
    console.log('🔧 Running in DEVELOPMENT mode');
  }

  // Enhanced startup recovery check (non-blocking)
  setImmediate(async () => {
    try {
      await checkStartupRecovery();

      // Initialize production database and users (especially for built apps)
      const isProduction = process.env.NODE_ENV === 'production' || process.pkg;

      if (isProduction) {
        console.log('🏭 Production mode detected - initializing production database...');
        const dbInitializer = new ProductionDbInitializer();
        await dbInitializer.initialize();
        await dbInitializer.disconnect();
      } else {
        console.log('🔧 Development mode - using standard user initialization...');
        await initializeProductionUsers();
      }

      // Save successful startup state
      await crashRecovery.saveApplicationState({
        serverStarted: true,
        port: PORT,
        environment: isDev ? 'development' : 'production',
        timestamp: new Date().toISOString(),
        globalErrorHandlerActive: true,
        crashRecoveryActive: true,
        usersInitialized: true
      });

      console.log('🛡️ Global error handling and crash recovery fully initialized');

      // Display memory configuration information
      const memStats = memoryConfig.getMemoryStats();
      if (isDev) {
        console.log('🔧 Development mode optimizations:');
        console.log(`   • Memory limit: ${memStats.maxMemoryMB}MB (--max-old-space-size=${memStats.maxMemoryMB})`);
        console.log('   • Memory threshold: 85% (vs 80% in production)');
        console.log('   • Crash threshold: 5 crashes (vs 2 in production)');
        console.log('   • Recovery sensitivity: Reduced for nodemon restarts');
      } else {
        console.log('🏭 Production mode optimizations:');
        console.log(`   • Memory limit: ${memStats.maxMemoryMB}MB (--max-old-space-size=${memStats.maxMemoryMB})`);
        console.log(`   • Garbage collection: ${memStats.gcEnabled ? 'Enabled' : 'Disabled'}`);
        console.log('   • Memory monitoring: Active');
        console.log('   • Automatic cleanup: Enabled');
        console.log('   • Performance optimizations: Active');
      }
    } catch (error) {
      console.error('Error during startup recovery check:', error);
      // Don't let recovery errors affect the application startup
    }
  });

  // Only initialize desktop email service in production or when explicitly enabled
  const enableEmailService = process.env.NODE_ENV === 'production' || process.env.ENABLE_EMAIL_SERVICE === 'true';

  if (enableEmailService) {
    console.log('📧 Initializing desktop email service...');
    desktopEmailService.initialize().catch(error => {
      console.error('❌ Failed to initialize desktop email service:', error);
    });

    // Initialize scheduled email service
    console.log('📅 Initializing scheduled email service...');
    const scheduledEmailService = require('./services/scheduledEmailService');
    scheduledEmailService.startScheduler();
    console.log('✅ Scheduled email service started (runs every 3 hours)');
  } else {
    console.log('📧 Desktop email service disabled in development mode');
    console.log('💡 Set ENABLE_EMAIL_SERVICE=true to enable email service in development');
  }

  // Initialize log maintenance service
  console.log('🗂️ Initializing log maintenance service...');
  const logMaintenanceService = require('./services/logMaintenanceService');
  logMaintenanceService.start();
  console.log('✅ Log maintenance service started (archives logs after 30 days, deletes after 90 days total)');

  // Initialize backup scheduler based on configuration
  try {
    console.log('☁️ Initializing backup scheduler...');

    // Read backup configuration to determine which scheduler to use
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(__dirname, '../cloud-backup-config.json');

    let backupScheduler;
    let backupStatus;

    if (fs.existsSync(configPath)) {
      const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

      switch (config.provider) {
        case 'email':
          console.log('📧 Using Email Backup Scheduler');
          backupScheduler = new EmailBackupScheduler();
          break;
        case 'hybrid':
          console.log('🔄 Using Hybrid Backup Scheduler');
          backupScheduler = new HybridBackupScheduler();
          break;
        case 'google-drive':
        case 'webhook':
        case 'dropbox':
          console.log('☁️ Using Cloud Backup Scheduler');
          backupScheduler = new CloudBackupScheduler();
          break;
        default:
          console.log('🔄 Using Hybrid Backup Scheduler (default)');
          backupScheduler = new HybridBackupScheduler();
      }
    } else {
      console.log('🔄 Using Hybrid Backup Scheduler (no config found)');
      backupScheduler = new HybridBackupScheduler();
    }

    await backupScheduler.startScheduler();
    backupStatus = backupScheduler.getStatus();

    if (backupStatus.enabled) {
      console.log('✅ Backup scheduler started (runs every 3 days)');
      console.log(`   • Provider: ${backupStatus.provider}`);
      console.log(`   • Last backup: ${backupStatus.lastBackup || 'Never'}`);
    } else {
      console.log('☁️ Backup is disabled');
      console.log('💡 Run Setup-Cloud-Backup.bat to enable automatic backups');
    }
  } catch (error) {
    console.error('❌ Failed to initialize backup scheduler:', error.message);
    console.log('💡 Backup will be disabled for this session');
  }
});

module.exports = app;
