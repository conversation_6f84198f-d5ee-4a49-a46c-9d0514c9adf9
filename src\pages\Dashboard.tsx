import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  BookOpenIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  ExclamationTriangleIcon,
  ChartBarIcon,
  CloudArrowUpIcon,
  PlusIcon,
  ArrowPathIcon,
  UserIcon,
  TagIcon,
  BuildingOfficeIcon,
  LanguageIcon,
  BookmarkIcon,
  AcademicCapIcon,
  MapPinIcon,
  ShieldCheckIcon,
  InboxIcon
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../store/authStore'
import MaithiliVikasKoshLogo from '../components/MaithiliVikasKoshLogo'

interface DashboardStats {
  books: {
    total: number
    available: number
    borrowed: number
  }
  members: {
    total: number
    active: number
    inactive: number
  }
  borrowings: {
    total: number
    active: number
    overdue: number
    returned: number
  }
  masters: {
    authors: number
    categories: number
    publishers: number
    languages: number
    bookSeries: number
    subjects: number
    locations: number
    conditions: number
    sources: number
  }
}

interface RecentActivity {
  id: string
  action: string
  entity: string
  details: string
  createdAt: string
  user: {
    name: string
    username: string
  }
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [recentActivities, setRecentActivities] = useState<RecentActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const location = useLocation()
  const { user, isAuthenticated } = useAuthStore()
  const hasInitiallyLoaded = useRef(false)

  // Memoize the fetch function to prevent unnecessary re-renders
  const fetchDashboardData = useCallback(async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setRefreshing(true)
      } else {
        setLoading(true)
      }

      // Check if user is authenticated
      if (!isAuthenticated || !user?.id) {
        setLoading(false)
        setRefreshing(false)
        return
      }

      // Use user.id directly to avoid dependency on entire user object
      const userId = user.id

      const [statsResponse, activitiesResponse] = await Promise.all([
        fetch('/api/reports/statistics', {
          credentials: 'include',
          headers: {
            'x-user-id': userId
          }
        }),
        fetch('/api/user-logs/recent?limit=10', {
          credentials: 'include',
          headers: {
            'x-user-id': userId
          }
        })
      ])

      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData)
      } else if (statsResponse.status === 401) {
        console.log('User not authorized for statistics')
      }

      if (activitiesResponse.ok) {
        const activitiesData = await activitiesResponse.json()
        setRecentActivities(activitiesData)
      } else if (activitiesResponse.status === 401) {
        console.log('User not authorized for activity logs')
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
      hasInitiallyLoaded.current = true
    }
  }, [isAuthenticated, user?.id]) // Only depend on user.id, not entire user object

  // Initial load only
  useEffect(() => {
    if (!hasInitiallyLoaded.current && isAuthenticated) {
      fetchDashboardData()
    }
  }, [fetchDashboardData, isAuthenticated])

  // Refresh on window focus (when user returns to the app) - with throttling
  useEffect(() => {
    let lastRefreshTime = 0
    const REFRESH_THROTTLE = 30000 // 30 seconds minimum between auto-refreshes

    const handleFocus = () => {
      const now = Date.now()
      if (
        document.visibilityState === 'visible' &&
        location.pathname === '/' &&
        isAuthenticated &&
        !refreshing &&
        !loading &&
        (now - lastRefreshTime) > REFRESH_THROTTLE
      ) {
        lastRefreshTime = now
        fetchDashboardData(true)
      }
    }

    document.addEventListener('visibilitychange', handleFocus)
    window.addEventListener('focus', handleFocus)

    return () => {
      document.removeEventListener('visibilitychange', handleFocus)
      window.removeEventListener('focus', handleFocus)
    }
  }, [fetchDashboardData, location.pathname, isAuthenticated, refreshing, loading])

  // Manual refresh function with debounce
  const handleRefresh = useCallback(() => {
    if (!refreshing && !loading) {
      fetchDashboardData(true)
    }
  }, [fetchDashboardData, refreshing, loading])

  const statCards = [
    {
      title: 'Total Books',
      value: stats?.books.total || 0,
      subtitle: `${stats?.books.available || 0} available, ${stats?.books.borrowed || 0} borrowed`,
      icon: BookOpenIcon,
      color: 'bg-blue-500',
      link: '/books'
    },
    {
      title: 'Total Members',
      value: stats?.members.total || 0,
      subtitle: `${stats?.members.active || 0} active, ${stats?.members.inactive || 0} inactive`,
      icon: UsersIcon,
      color: 'bg-green-500',
      link: '/members'
    },
    {
      title: 'Active Borrowings',
      value: stats?.borrowings.active || 0,
      subtitle: `${stats?.borrowings.overdue || 0} overdue`,
      icon: ClipboardDocumentListIcon,
      color: 'bg-yellow-500',
      link: '/borrowings'
    },
    {
      title: 'Overdue Books',
      value: stats?.borrowings.overdue || 0,
      subtitle: 'Require immediate attention',
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      link: '/borrowings?status=OVERDUE'
    }
  ]

  const quickActions = [
    {
      title: 'Add New Book',
      description: 'Register a new book in the library',
      icon: BookOpenIcon,
      link: '/books/new',
      color: 'bg-blue-500'
    },
    {
      title: 'Add New Member',
      description: 'Register a new library member',
      icon: UsersIcon,
      link: '/members/new',
      color: 'bg-green-500'
    },
    {
      title: 'Issue Book',
      description: 'Issue a book to a member',
      icon: ClipboardDocumentListIcon,
      link: '/borrowings/new',
      color: 'bg-yellow-500'
    },

    {
      title: 'Backup Data',
      description: 'Create backup of library data',
      icon: CloudArrowUpIcon,
      link: '/backup-restore',
      color: 'bg-indigo-500'
    }
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="spinner spinner-lg" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-white rounded-xl p-2 shadow-lg">
                <MaithiliVikasKoshLogo
                  className="w-full h-full object-contain"
                  width="100%"
                  height="100%"
                />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-bold mb-2">मैथिली विकास कोष</h1>
              <p className="text-xs text-primary-200 opacity-75 -mt-1 mb-2">Maithili Vikas Kosh</p>
              <p className="text-primary-100 text-xl font-semibold">विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्द्र</p>
              <p className="text-xs text-primary-200 opacity-75 -mt-1 mb-3">Vidyapati Library - Study and Research Center</p>
              <p className="text-primary-100 mt-2">Manage your library efficiently with our comprehensive system</p>
            </div>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Refresh dashboard data"
          >
            <ArrowPathIcon className={`w-5 h-5 ${refreshing ? 'animate-spin' : ''}`} />
            <span className="text-sm font-medium">
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </span>
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 desktop-xl:grid-cols-6 ultrawide:grid-cols-8 4k:grid-cols-10 5k:grid-cols-12 8k:grid-cols-16 gap-6">
        {statCards.map((card, index) => (
          <Link
            key={index}
            to={card.link}
            className="card hover:shadow-medium transition-shadow duration-200"
          >
            <div className="card-body">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${card.color}`}>
                  <card.icon className="w-6 h-6 text-white" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600">{card.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{card.value}</p>
                  <p className="text-xs text-gray-500">{card.subtitle}</p>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Catalog Section */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Catalog Overview</h3>
          <p className="text-sm text-gray-600 mt-1">Master data and reference counts</p>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 desktop-xl:grid-cols-7 ultrawide:grid-cols-9 4k:grid-cols-11 5k:grid-cols-13 8k:grid-cols-18 gap-4">
            {/* Authors */}
            <Link
              to="/authors"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <UserIcon className="w-8 h-8 text-blue-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.authors || 0}</p>
              <p className="text-sm font-medium text-gray-700">Authors</p>
              <p className="text-xs text-gray-500">लेखक</p>
            </Link>

            {/* Categories */}
            <Link
              to="/categories"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <TagIcon className="w-8 h-8 text-green-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.categories || 0}</p>
              <p className="text-sm font-medium text-gray-700">Categories</p>
              <p className="text-xs text-gray-500">श्रेणी</p>
            </Link>

            {/* Publishers */}
            <Link
              to="/publishers"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <BuildingOfficeIcon className="w-8 h-8 text-purple-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.publishers || 0}</p>
              <p className="text-sm font-medium text-gray-700">Publishers</p>
              <p className="text-xs text-gray-500">प्रकाशक</p>
            </Link>

            {/* Languages */}
            <Link
              to="/languages"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <LanguageIcon className="w-8 h-8 text-indigo-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.languages || 0}</p>
              <p className="text-sm font-medium text-gray-700">Languages</p>
              <p className="text-xs text-gray-500">भाषा</p>
            </Link>

            {/* Book Series */}
            <Link
              to="/book-series"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <BookmarkIcon className="w-8 h-8 text-yellow-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.bookSeries || 0}</p>
              <p className="text-sm font-medium text-gray-700">Book Series</p>
              <p className="text-xs text-gray-500">पुस्तक श्रृंखला</p>
            </Link>

            {/* Subjects */}
            <Link
              to="/subjects"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <AcademicCapIcon className="w-8 h-8 text-red-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.subjects || 0}</p>
              <p className="text-sm font-medium text-gray-700">Subjects</p>
              <p className="text-xs text-gray-500">विषय</p>
            </Link>

            {/* Locations */}
            <Link
              to="/locations"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <MapPinIcon className="w-8 h-8 text-teal-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.locations || 0}</p>
              <p className="text-sm font-medium text-gray-700">Locations</p>
              <p className="text-xs text-gray-500">स्थान</p>
            </Link>

            {/* Conditions */}
            <Link
              to="/conditions"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <ShieldCheckIcon className="w-8 h-8 text-orange-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.conditions || 0}</p>
              <p className="text-sm font-medium text-gray-700">Conditions</p>
              <p className="text-xs text-gray-500">स्थिति</p>
            </Link>

            {/* Sources */}
            <Link
              to="/sources"
              className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group text-center"
            >
              <InboxIcon className="w-8 h-8 text-pink-600 mx-auto mb-2 group-hover:scale-110 transition-transform duration-200" />
              <p className="text-2xl font-bold text-gray-900">{stats?.masters.sources || 0}</p>
              <p className="text-sm font-medium text-gray-700">Sources</p>
              <p className="text-xs text-gray-500">स्रोत</p>
            </Link>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 desktop-xl:grid-cols-3 ultrawide:grid-cols-4 4k:grid-cols-5 5k:grid-cols-6 8k:grid-cols-8 gap-6">
        {/* Quick Actions */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
          </div>
          <div className="card-body">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-2 desktop-xl:grid-cols-3 ultrawide:grid-cols-4 4k:grid-cols-5 5k:grid-cols-6 8k:grid-cols-8 gap-4">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  to={action.link}
                  className="p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:shadow-soft transition-all duration-200 group"
                >
                  <div className="flex items-center mb-3">
                    <div className={`p-2 rounded-lg ${action.color} group-hover:scale-110 transition-transform duration-200`}>
                      <action.icon className="w-5 h-5 text-white" />
                    </div>
                    <PlusIcon className="w-4 h-4 text-gray-400 ml-auto" />
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">{action.title}</h4>
                  <p className="text-sm text-gray-600">{action.description}</p>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
          </div>
          <div className="card-body">
            {recentActivities.length > 0 ? (
              <div className="space-y-4">
                {recentActivities.slice(0, 8).map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <div className="w-2 h-2 bg-primary-500 rounded-full mt-2" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-900">
                        <span className="font-medium">{activity.user.name}</span>{' '}
                        <span className="text-gray-600">{activity.action.toLowerCase()}</span>{' '}
                        <span className="font-medium">{activity.entity.toLowerCase()}</span>
                      </p>
                      {activity.details && (
                        <p className="text-xs text-gray-500 mt-1">{activity.details}</p>
                      )}
                      <p className="text-xs text-gray-400 mt-1">
                        {new Date(activity.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))}
                <div className="pt-4 border-t border-gray-200">
                  <Link
                    to="/user-logs"
                    className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                  >
                    View all activities →
                  </Link>
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <ClipboardDocumentListIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No recent activities</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Library Information */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">Library Information</h3>
        </div>
        <div className="card-body">
          <div className="grid grid-cols-1 md:grid-cols-3 desktop-xl:grid-cols-4 ultrawide:grid-cols-6 4k:grid-cols-8 5k:grid-cols-10 8k:grid-cols-12 gap-6">
            <div className="text-center">
              <BookOpenIcon className="w-8 h-8 text-primary-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">Collection</h4>
              <p className="text-sm text-gray-600 mt-1">
                {stats?.masters.authors || 0} Authors, {stats?.masters.categories || 0} Categories
              </p>
              <p className="text-xs text-gray-500 mt-1">
                {stats?.masters.publishers || 0} Publishers, {stats?.masters.languages || 0} Languages
              </p>
            </div>
            <div className="text-center">
              <UsersIcon className="w-8 h-8 text-green-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">Membership</h4>
              <p className="text-sm text-gray-600 mt-1">
                Active community of {stats?.members.active || 0} members
              </p>
            </div>
            <div className="text-center">
              <ChartBarIcon className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900">Activity</h4>
              <p className="text-sm text-gray-600 mt-1">
                {stats?.borrowings.total || 0} total borrowings recorded
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Developer Information */}
      <div className="card">
        <div className="card-body">
          <div className="text-center py-4">
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-700">Developed by</span>
                <span className="font-semibold text-primary-600">श्रद्धेय वात्स्यायन</span>
              </div>
              <div className="hidden sm:block text-gray-300">|</div>
              <div className="flex items-center space-x-2">
                <span>📞</span>
                <span className="font-medium">9844361480</span>
              </div>
              <div className="hidden sm:block text-gray-300">|</div>
              <div>
                <span className="text-gray-500">© 2025 All rights reserved.</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
