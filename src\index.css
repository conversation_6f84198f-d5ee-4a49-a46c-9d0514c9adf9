@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply text-gray-900 font-sans;
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Optimize for fullscreen transitions */
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
    /* Prevent flickering during fullscreen transitions */
    will-change: transform;
  }

  html {
    scroll-behavior: smooth;
    /* Optimize rendering for Electron */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Prevent layout shifts during fullscreen */
    overflow-x: hidden;
  }

  /* Fullscreen optimization styles */
  html:-webkit-full-screen,
  html:-moz-full-screen,
  html:fullscreen {
    /* Disable transitions during fullscreen to prevent flickering */
    * {
      transition: none !important;
      animation: none !important;
    }
  }

  /* Re-enable transitions after fullscreen transition completes */
  html:not(:-webkit-full-screen):not(:-moz-full-screen):not(:fullscreen) {
    * {
      transition: all 0.2s ease-out;
    }
  }

  /* Electron-specific optimizations */
  @media screen and (-webkit-min-device-pixel-ratio: 1) {
    body {
      /* Force hardware acceleration */
      -webkit-transform: translate3d(0, 0, 0);
      transform: translate3d(0, 0, 0);
    }
  }
}

@layer components {
  /* Windows-Style Button Components */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed border;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }

  .btn:hover {
    transform: translateY(-1px);
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }

  .btn:active {
    transform: translateY(0);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05);
  }

  .btn-primary {
    @apply btn text-white focus:ring-primary-500 border-primary-700;
    background: linear-gradient(180deg, #475569 0%, #334155 100%);
  }

  .btn-primary:hover {
    background: linear-gradient(180deg, #334155 0%, #1e293b 100%);
  }

  .btn-secondary {
    @apply btn text-secondary-700 focus:ring-secondary-500 border-secondary-300;
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  .btn-secondary:hover {
    background: linear-gradient(180deg, #e2e8f0 0%, #cbd5e1 100%);
  }

  .btn-success {
    @apply btn text-white focus:ring-success-500 border-success-700;
    background: linear-gradient(180deg, #16a34a 0%, #15803d 100%);
  }

  .btn-success:hover {
    background: linear-gradient(180deg, #15803d 0%, #166534 100%);
  }

  .btn-warning {
    @apply btn text-white focus:ring-warning-500 border-warning-700;
    background: linear-gradient(180deg, #d97706 0%, #b45309 100%);
  }

  .btn-warning:hover {
    background: linear-gradient(180deg, #b45309 0%, #92400e 100%);
  }

  .btn-danger {
    @apply btn text-white focus:ring-accent-500 border-accent-700;
    background: linear-gradient(180deg, #dc2626 0%, #b91c1c 100%);
  }

  .btn-danger:hover {
    background: linear-gradient(180deg, #b91c1c 0%, #991b1b 100%);
  }

  .btn-ghost {
    @apply btn text-gray-600 focus:ring-gray-500 border-transparent;
    background: transparent;
    box-shadow: none;
  }

  .btn-ghost:hover {
    @apply bg-gray-100 text-gray-900;
    background: linear-gradient(180deg, #f3f4f6 0%, #e5e7eb 100%);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-md {
    @apply px-4 py-2 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Windows-Style Input Components */
  .input {
    @apply block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05),
      0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .input:focus {
    @apply border-primary-500;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05),
      0 0 0 3px rgba(71, 85, 105, 0.1);
  }

  .input:disabled {
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    @apply text-gray-500;
  }

  .input-error {
    @apply border-accent-300 focus:border-accent-500 focus:ring-accent-500;
    box-shadow:
      inset 0 1px 3px rgba(220, 38, 38, 0.1),
      inset 0 1px 0 rgba(220, 38, 38, 0.05),
      0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .input-error:focus {
    box-shadow:
      inset 0 1px 3px rgba(220, 38, 38, 0.1),
      inset 0 1px 0 rgba(220, 38, 38, 0.05),
      0 0 0 3px rgba(220, 38, 38, 0.1);
  }

  .label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .label-required::after {
    @apply text-accent-500 ml-1;
    content: "*";
  }

  /* Windows-Style Card Components */
  .card {
    @apply bg-white rounded-lg border border-gray-300 overflow-hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  /* Special card variant for dropdowns */
  .card-with-dropdown {
    @apply bg-white rounded-lg border border-gray-300;
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Ensure proper stacking context for dropdowns */
    position: relative;
    z-index: auto; /* Changed from 1 to auto to prevent stacking context issues */
    /* Allow dropdowns to overflow the card boundaries */
    overflow: visible !important;
  }

  /* Ensure Publication Details section doesn't interfere with dropdowns */
  .card-with-dropdown.publication-details {
    z-index: auto; /* Changed from 10 to auto to prevent stacking context issues */
    /* Ensure this card has proper positioning */
    position: relative;
    /* Add extra bottom margin to prevent overlap */
    margin-bottom: 2rem;
    /* Ensure dropdowns can overflow */
    overflow: visible !important;
  }

  /* Special override for publisher dropdown within publication details */
  .card-with-dropdown.publication-details .publisher-dropdown-container {
    z-index: 99999 !important;
    position: relative;
    /* Force new stacking context */
    isolation: isolate;
  }

  /* Ensure language dropdown container has proper stacking */
  .language-dropdown-container {
    z-index: 10500;
    position: relative;
  }

  /* Ensure dropdown containers have proper stacking */
  .author-dropdown-container {
    z-index: 999998;
    position: relative;
    margin-bottom: 2rem !important;
    /* Force stacking context */
    isolation: isolate;
    /* Ensure container doesn't clip dropdown */
    overflow: visible !important;
  }

  .category-dropdown-container {
    z-index: 999997;
    position: relative;
    margin-bottom: 2rem !important;
    /* Force stacking context */
    isolation: isolate;
    /* Ensure container doesn't clip dropdown */
    overflow: visible !important;
  }

  .subject-dropdown-container {
    z-index: 999996;
    position: relative;
    margin-bottom: 2rem !important;
    /* Force stacking context */
    isolation: isolate;
    /* Ensure container doesn't clip dropdown */
    overflow: visible !important;
  }

  .series-dropdown-container {
    z-index: 999995;
    position: relative;
    margin-bottom: 2rem !important;
    /* Force stacking context */
    isolation: isolate;
    /* Ensure container doesn't clip dropdown */
    overflow: visible !important;
  }

  .publisher-dropdown-container {
    z-index: 99999;
    position: relative;
    margin-bottom: 2rem !important;
    /* Force stacking context */
    isolation: isolate;
    /* Ensure container doesn't clip dropdown */
    overflow: visible !important;
  }

  .card:hover,
  .card-with-dropdown:hover {
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.12),
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
    transition: all 0.2s ease-out;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-300;
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .card-body {
    @apply px-6 py-4;
    background: linear-gradient(180deg, #ffffff 0%, #fefefe 100%);
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-300;
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  /* Windows-Style Table Components */
  .table {
    @apply w-full divide-y divide-gray-300 bg-white rounded-lg overflow-hidden border border-gray-300;
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .table-modern {
    @apply w-full bg-white rounded-lg overflow-hidden border border-gray-300 divide-y divide-gray-300;
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.1),
      0 2px 6px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .table-header {
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    @apply border-b border-gray-300;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .table-header-modern {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    @apply border-b border-gray-300;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .table-header th {
    @apply px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider;
  }

  .table-header-modern th {
    @apply px-6 py-4 text-left text-sm font-semibold text-gray-700 uppercase tracking-wide;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-body td {
    @apply px-6 py-4 text-sm text-gray-900;
  }

  .table-body-modern {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-body-modern td {
    @apply px-6 py-5 text-sm text-gray-900;
  }

  .table-row-hover {
    @apply transition-all duration-200 cursor-pointer;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  }

  .table-row-hover:hover {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .table-row-selected {
    @apply bg-gradient-to-r from-primary-50 to-primary-100 border-l-4 border-primary-500;
  }

  .table-row-striped:nth-child(even) {
    @apply bg-secondary-25;
  }

  /* Table Size Variants */
  .table-compact td {
    @apply px-4 py-2 text-xs;
  }

  .table-compact th {
    @apply px-4 py-2 text-xs;
  }

  .table-comfortable td {
    @apply px-6 py-4 text-sm;
  }

  .table-comfortable th {
    @apply px-6 py-3 text-xs;
  }

  .table-spacious td {
    @apply px-8 py-6 text-base;
  }

  .table-spacious th {
    @apply px-8 py-4 text-sm;
  }

  /* Table States */
  .table-loading {
    @apply opacity-60 pointer-events-none;
  }

  .table-empty {
    @apply text-center text-gray-500 py-12;
  }

  /* Sortable Headers */
  .table-sortable th {
    @apply cursor-pointer hover:bg-gray-100 transition-colors duration-150 select-none;
  }

  .table-sort-asc::after {
    content: ' ↑';
    @apply text-primary-600 font-bold;
  }

  .table-sort-desc::after {
    content: ' ↓';
    @apply text-primary-600 font-bold;
  }

  /* Windows-Style Action Buttons in Tables */
  .table-action-btn {
    @apply inline-flex items-center justify-center w-8 h-8 rounded-lg transition-all duration-200 border;
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    border-color: #d1d5db;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .table-action-btn:hover {
    transform: translateY(-1px);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .table-action-btn:active {
    transform: translateY(0);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05);
  }

  .table-action-btn-edit {
    @apply table-action-btn text-gray-500 hover:text-primary-600;
  }

  .table-action-btn-edit:hover {
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #cbd5e1;
  }

  .table-action-btn-delete {
    @apply table-action-btn text-gray-500 hover:text-accent-600;
  }

  .table-action-btn-delete:hover {
    background: linear-gradient(180deg, #fef2f2 0%, #fee2e2 100%);
    border-color: #fca5a5;
  }

  .table-action-btn-view {
    @apply table-action-btn text-gray-500 hover:text-secondary-600;
  }

  .table-action-btn-view:hover {
    background: linear-gradient(180deg, #f9fafb 0%, #f3f4f6 100%);
    border-color: #d1d5db;
  }

  /* Enhanced Table Features */
  .table-cell-multiline {
    @apply whitespace-normal leading-relaxed;
  }

  .table-cell-numeric {
    @apply text-right font-mono;
  }

  .table-cell-badge {
    @apply text-center;
  }

  /* Optional Column Dividers */
  .table-with-column-dividers th:not(:last-child),
  .table-with-column-dividers td:not(:last-child) {
    @apply border-r border-gray-100;
  }

  .table-with-column-dividers-strong th:not(:last-child),
  .table-with-column-dividers-strong td:not(:last-child) {
    @apply border-r border-gray-200;
  }

  /* Comprehensive Responsive Design for All Desktop Sizes */

  /* Extra Small Desktops/Large Tablets (1024px - 1199px) */
  @media (max-width: 1199px) and (min-width: 1024px) {
    .table-modern {
      @apply text-sm;
    }

    .table-header-modern th {
      @apply px-4 py-3 text-sm;
    }

    .table-body-modern td {
      @apply px-4 py-3 text-sm;
    }

    .btn-lg {
      @apply px-4 py-2 text-sm;
    }

    .card-header {
      @apply px-4 py-3;
    }

    .card-body {
      @apply px-4 py-3;
    }
  }

  /* Small Desktops/Laptops (768px - 1023px) */
  @media (max-width: 1023px) and (min-width: 768px) {
    .table-modern {
      @apply text-xs;
    }

    .table-header-modern th {
      @apply px-3 py-2 text-xs;
    }

    .table-body-modern td {
      @apply px-3 py-2 text-xs;
    }

    .table-action-btn {
      @apply w-6 h-6;
    }

    .btn {
      @apply px-3 py-1.5 text-xs;
    }

    .card {
      @apply text-sm;
    }

    .card-header {
      @apply px-3 py-2;
    }

    .card-body {
      @apply px-3 py-2;
    }
  }

  /* Tablets and Small Screens (max-width: 767px) */
  @media (max-width: 767px) {
    .table-modern {
      @apply text-xs;
    }

    .table-header-modern th {
      @apply px-2 py-2 text-xs;
    }

    .table-body-modern td {
      @apply px-2 py-2 text-xs;
    }

    .table-action-btn {
      @apply w-5 h-5;
    }

    .btn {
      @apply px-2 py-1 text-xs;
    }

    .card-header {
      @apply px-2 py-2;
    }

    .card-body {
      @apply px-2 py-2;
    }
  }

  /* Large Desktops (1920px and above) */
  @media (min-width: 1920px) {
    .table-modern {
      @apply text-base;
    }

    .table-header-modern th {
      @apply px-8 py-4 text-base;
    }

    .table-body-modern td {
      @apply px-8 py-4 text-base;
    }

    .btn-lg {
      @apply px-8 py-4 text-lg;
    }

    .card-header {
      @apply px-8 py-5;
    }

    .card-body {
      @apply px-8 py-5;
    }
  }

  /* Table Loading Animation */
  .table-skeleton {
    @apply animate-pulse;
  }

  .table-skeleton td {
    @apply bg-gray-200 rounded;
  }

  /* Windows-Style Status Badges */
  .status-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold border;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .status-badge-success {
    @apply status-badge text-success-800 border-success-300;
    background: linear-gradient(180deg, #dcfce7 0%, #bbf7d0 100%);
  }

  .status-badge-warning {
    @apply status-badge text-warning-800 border-warning-300;
    background: linear-gradient(180deg, #fef3c7 0%, #fde68a 100%);
  }

  .status-badge-danger {
    @apply status-badge text-accent-800 border-accent-300;
    background: linear-gradient(180deg, #fee2e2 0%, #fecaca 100%);
  }

  .status-badge-info {
    @apply status-badge text-primary-800 border-primary-300;
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  .status-badge-secondary {
    @apply status-badge text-secondary-800 border-secondary-300;
    background: linear-gradient(180deg, #f3f4f6 0%, #e5e7eb 100%);
  }

  /* Enhanced Searchable Dropdown Styles */
  .searchable-dropdown {
    @apply relative;
    /* Ensure proper stacking context for dropdown */
    z-index: 1;
    /* Add bottom margin to prevent overlap with next form field */
    margin-bottom: 1.5rem;
  }

  /* Ensure dropdown container doesn't create stacking context issues */
  .searchable-dropdown-container {
    @apply relative;
    z-index: auto;
    /* Ensure container allows overflow for dropdown positioning */
    overflow: visible !important;
  }

  .searchable-dropdown-input {
    @apply block w-full rounded-lg border border-gray-300 px-3 py-2 text-sm placeholder-gray-400 transition-all duration-200 focus:outline-none focus:ring-1 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed;
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05),
      0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .searchable-dropdown-input:focus {
    @apply border-primary-500;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05),
      0 0 0 3px rgba(71, 85, 105, 0.1);
  }

  .searchable-dropdown-menu {
    @apply absolute w-full bg-white border border-gray-300 rounded-lg overflow-hidden;
    z-index: 9999 !important; /* Ensure dropdown is always on top */
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    box-shadow:
      0 4px 12px rgba(0, 0, 0, 0.15),
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Ensure dropdown appears above all other content with proper spacing */
    position: absolute !important;
    top: 100% !important; /* Position directly below input */
    left: 0 !important;
    right: 0 !important;
    margin-top: 4px !important; /* Add gap between input and dropdown */
  }

  /* Specific z-index hierarchy for add book form dropdowns */
  .searchable-dropdown-menu.z-author {
    z-index: 999998 !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.25),
      0 8px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Force positioning above everything */
    position: absolute !important;
    /* Ensure it's not clipped by any parent */
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  .searchable-dropdown-menu.z-category {
    z-index: 999997 !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.25),
      0 8px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Force positioning above everything */
    position: absolute !important;
    /* Ensure it's not clipped by any parent */
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  .searchable-dropdown-menu.z-subject {
    z-index: 999996 !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.25),
      0 8px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Force positioning above everything */
    position: absolute !important;
    /* Ensure it's not clipped by any parent */
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  .searchable-dropdown-menu.z-series {
    z-index: 999995 !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.25),
      0 8px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Force positioning above everything */
    position: absolute !important;
    /* Ensure it's not clipped by any parent */
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  .searchable-dropdown-menu.z-publisher {
    z-index: 99999 !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.25),
      0 8px 20px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
    /* Force positioning above everything */
    position: absolute !important;
    /* Ensure it's not clipped by any parent */
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
  }

  .searchable-dropdown-menu.z-language {
    z-index: 10500 !important;
    /* Ensure language dropdown appears above all sections */
    position: absolute !important;
    /* Enhanced shadow for better visibility over sections */
    box-shadow:
      0 12px 30px rgba(0, 0, 0, 0.2),
      0 6px 15px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .searchable-dropdown-menu.z-location {
    z-index: 9993 !important;
  }

  .searchable-dropdown-menu.z-condition {
    z-index: 9992 !important;
  }

  .searchable-dropdown-menu.z-source {
    z-index: 9991 !important;
  }

  /* Enhanced dropdown visibility and interaction */
  .searchable-dropdown-menu {
    /* Ensure dropdown is always visible and properly positioned */
    position: absolute !important;
    top: calc(100% + 4px) !important;
    left: 0 !important;
    right: 0 !important;
    /* Prevent dropdown from being cut off by parent containers */
    transform: none !important;
    /* Enhanced shadow for better visibility */
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.15),
      0 4px 10px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05) !important;
    /* Ensure dropdown has proper background */
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    /* Add border for better definition */
    border: 1px solid #d1d5db !important;
    /* Ensure dropdown content is not transparent */
    opacity: 1 !important;
  }

  /* Additional spacing for form fields with dropdowns to prevent overlap */
  .card-with-dropdown .grid > div:has(.searchable-dropdown) {
    margin-bottom: 2rem;
  }

  /* Ensure proper spacing between form grid items */
  .card-with-dropdown .grid.gap-6 > div {
    margin-bottom: 1rem;
  }

  /* Override for last items in grid to prevent excessive bottom spacing */
  .card-with-dropdown .grid > div:last-child,
  .card-with-dropdown .grid > div:nth-last-child(1) {
    margin-bottom: 0;
  }

  /* Special handling for two-column grids */
  .card-with-dropdown .grid.md\\:grid-cols-2 > div:nth-last-child(-n+2) {
    margin-bottom: 0;
  }

  /* Special spacing for language dropdown to prevent overlap with next section */
  .language-dropdown-container {
    margin-bottom: 3rem !important;
    /* Ensure the container creates proper stacking context */
    position: relative;
    z-index: 10500;
  }

  /* Ensure language dropdown menu appears above everything */
  .language-dropdown-container .searchable-dropdown-menu {
    z-index: 10500 !important;
    /* Force the dropdown to appear above all other content */
    position: absolute !important;
    /* Ensure it's not clipped by parent containers */
    clip: unset !important;
    overflow: visible !important;
  }

  /* Ensure publisher dropdown menu appears properly */
  .publisher-dropdown-container .searchable-dropdown-menu {
    z-index: 99999 !important;
    /* Force the dropdown to appear above all other content */
    position: absolute !important;
    /* Ensure it's not clipped by parent containers */
    clip: unset !important;
    overflow: visible !important;
    /* Ensure proper width and positioning */
    width: 100% !important;
    left: 0 !important;
    right: 0 !important;
    top: calc(100% + 4px) !important;
    /* Force visibility */
    visibility: visible !important;
    opacity: 1 !important;
    /* Prevent any transforms that might hide it */
    transform: none !important;
    /* Ensure it's above everything */
    background: white !important;
    border: 1px solid #d1d5db !important;
  }

  /* Ensure proper spacing and positioning for dropdown menus */
  .searchable-dropdown {
    position: relative;
    /* Add minimum height to prevent layout shifts */
    min-height: 2.5rem;
  }

  /* Ensure dropdown appears with proper spacing and doesn't overlap */
  .searchable-dropdown .searchable-dropdown-menu {
    /* Ensure dropdown width matches input */
    width: 100% !important;
    /* Prevent dropdown from being cut off */
    max-width: none !important;
  }

  /* Special handling for all dropdown containers to ensure visibility */
  .author-dropdown-container .searchable-dropdown {
    z-index: 999998 !important;
    position: relative;
    /* Ensure no clipping */
    overflow: visible !important;
    /* Override the base searchable-dropdown z-index */
    isolation: isolate;
  }

  .category-dropdown-container .searchable-dropdown {
    z-index: 999997 !important;
    position: relative;
    /* Ensure no clipping */
    overflow: visible !important;
    /* Override the base searchable-dropdown z-index */
    isolation: isolate;
  }

  .subject-dropdown-container .searchable-dropdown {
    z-index: 999996 !important;
    position: relative;
    /* Ensure no clipping */
    overflow: visible !important;
    /* Override the base searchable-dropdown z-index */
    isolation: isolate;
  }

  .series-dropdown-container .searchable-dropdown {
    z-index: 999995 !important;
    position: relative;
    /* Ensure no clipping */
    overflow: visible !important;
    /* Override the base searchable-dropdown z-index */
    isolation: isolate;
  }

  .publisher-dropdown-container .searchable-dropdown {
    z-index: 99999 !important;
    position: relative;
    /* Ensure no clipping */
    overflow: visible !important;
    /* Override the base searchable-dropdown z-index */
    isolation: isolate;
  }

  /* Force publisher dropdown input to have proper stacking */
  .publisher-dropdown-container .searchable-dropdown-input {
    z-index: 99998;
    position: relative;
  }

  /* Ultimate override for all dropdown visibility */
  .author-dropdown-container .searchable-dropdown-menu,
  .searchable-dropdown-menu.z-author {
    z-index: 999998 !important;
    position: absolute !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force it to appear above everything */
    isolation: isolate;
  }

  .category-dropdown-container .searchable-dropdown-menu,
  .searchable-dropdown-menu.z-category {
    z-index: 999997 !important;
    position: absolute !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force it to appear above everything */
    isolation: isolate;
  }

  .subject-dropdown-container .searchable-dropdown-menu,
  .searchable-dropdown-menu.z-subject {
    z-index: 999996 !important;
    position: absolute !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force it to appear above everything */
    isolation: isolate;
  }

  .series-dropdown-container .searchable-dropdown-menu,
  .searchable-dropdown-menu.z-series {
    z-index: 999995 !important;
    position: absolute !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force it to appear above everything */
    isolation: isolate;
  }

  .publisher-dropdown-container .searchable-dropdown-menu,
  .searchable-dropdown-menu.z-publisher {
    z-index: 999999 !important;
    position: absolute !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    transform: none !important;
    clip: unset !important;
    overflow: visible !important;
    /* Force it to appear above everything */
    isolation: isolate;
  }

  /* Responsive dropdown improvements */
  @media (max-width: 768px) {
    .searchable-dropdown-menu {
      /* Reduce max height on mobile for better usability */
      max-height: 200px !important;
      /* Ensure dropdown doesn't extend beyond viewport */
      max-width: calc(100vw - 2rem) !important;
    }

    .searchable-dropdown-option {
      /* Increase touch target size on mobile */
      padding: 0.75rem 1rem !important;
    }

    .card-with-dropdown .grid {
      /* Single column on mobile for better dropdown visibility */
      grid-template-columns: 1fr !important;
      gap: 1.5rem !important;
    }

    .card-with-dropdown .grid > div {
      margin-bottom: 1.5rem !important;
    }
  }

  /* Enhanced focus states for better accessibility */
  .searchable-dropdown-input:focus {
    /* Enhanced focus ring for better visibility */
    box-shadow:
      inset 0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05),
      0 0 0 3px rgba(71, 85, 105, 0.15) !important;
    border-color: #475569 !important;
  }

  /* Improved loading state styling */
  .searchable-dropdown-loading {
    background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
  }

  @keyframes loading-shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }

  .searchable-dropdown-option {
    @apply px-4 py-3 cursor-pointer border-b border-gray-100 last:border-b-0 transition-all duration-150;
    background: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
  }

  .searchable-dropdown-option:hover {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .searchable-dropdown-option.highlighted {
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .searchable-dropdown-option.selected {
    background: linear-gradient(180deg, #dbeafe 0%, #bfdbfe 100%);
    @apply border-primary-200;
  }

  .searchable-dropdown-group-header {
    @apply px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-200;
    background: linear-gradient(180deg, #f9fafb 0%, #f3f4f6 100%);
    box-shadow:
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .searchable-dropdown-loading {
    @apply px-4 py-3 text-sm text-gray-500 text-center;
  }

  .searchable-dropdown-empty {
    @apply px-4 py-3 text-sm text-gray-500 text-center;
  }

  /* Spinner for loading states */
  .spinner {
    @apply inline-block animate-spin rounded-full border-2 border-solid border-current border-r-transparent;
  }

  .spinner-sm {
    @apply w-4 h-4;
  }

  .spinner-md {
    @apply w-6 h-6;
  }

  .spinner-lg {
    @apply w-8 h-8;
  }

  /* Windows-Style Alert Components */
  .alert {
    @apply flex items-start p-4 rounded-lg border;
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .alert-success {
    @apply alert border-success-300 text-success-800;
    background: linear-gradient(180deg, #f0fdf4 0%, #dcfce7 100%);
  }

  .alert-warning {
    @apply alert border-warning-300 text-warning-800;
    background: linear-gradient(180deg, #fffbeb 0%, #fef3c7 100%);
  }

  .alert-error {
    @apply alert border-accent-300 text-accent-800;
    background: linear-gradient(180deg, #fef2f2 0%, #fee2e2 100%);
  }

  .alert-info {
    @apply alert border-primary-300 text-primary-800;
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  }

  .alert-dismissible {
    @apply pr-12 relative;
  }

  .alert-dismiss-btn {
    @apply absolute top-3 right-3 text-gray-400 hover:text-gray-600 transition-colors duration-200;
  }

  .alert-icon {
    @apply flex-shrink-0 w-5 h-5 mr-3 mt-0.5;
  }

  .alert-content {
    @apply flex-1 min-w-0;
  }

  .alert-title {
    @apply font-semibold text-sm mb-1;
  }

  .alert-message {
    @apply text-sm;
  }

  .alert-actions {
    @apply mt-3 flex space-x-2;
  }

  .alert-action-btn {
    @apply text-xs font-medium px-3 py-1 rounded-md transition-colors duration-200;
  }

  .alert-action-primary {
    @apply alert-action-btn bg-primary-600 text-white hover:bg-primary-700;
  }

  .alert-action-secondary {
    @apply alert-action-btn bg-white text-gray-700 border border-gray-300 hover:bg-gray-50;
  }

  /* Windows-Style Badge Components */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .badge-success {
    @apply text-success-800 border-success-300;
    background: linear-gradient(180deg, #dcfce7 0%, #bbf7d0 100%);
  }

  .badge-warning {
    @apply text-warning-800 border-warning-300;
    background: linear-gradient(180deg, #fef3c7 0%, #fde68a 100%);
  }

  .badge-danger {
    @apply text-accent-800 border-accent-300;
    background: linear-gradient(180deg, #fee2e2 0%, #fecaca 100%);
  }

  .badge-info {
    @apply text-primary-800 border-primary-300;
    background: linear-gradient(180deg, #f1f5f9 0%, #e2e8f0 100%);
  }

  .badge-secondary {
    @apply text-secondary-800 border-secondary-300;
    background: linear-gradient(180deg, #f3f4f6 0%, #e5e7eb 100%);
  }

  /* Loading Components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }

  .spinner-sm {
    @apply w-4 h-4;
  }

  .spinner-md {
    @apply w-6 h-6;
  }

  .spinner-lg {
    @apply w-8 h-8;
  }

  /* Utility Classes */
  .text-nepali {
    @apply font-nepali;
  }

  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }

  .shadow-medium {
    box-shadow: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .shadow-strong {
    box-shadow: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1);
  }

  /* Z-index utilities for debugging */
  .z-dropdown {
    z-index: 9999 !important;
  }

  .z-modal {
    z-index: 10000 !important;
  }

  .z-tooltip {
    z-index: 10001 !important;
  }

  /* Windows-Style Sidebar Components */
  .sidebar-windows {
    @apply bg-gradient-to-b from-gray-50 to-gray-100 border-r border-gray-300;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1), inset -1px 0 0 rgba(255, 255, 255, 0.5);
    /* Ensure sidebar stays fixed and doesn't scroll with content */
    position: fixed !important;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 30;
  }

  .sidebar-header-windows {
    @apply bg-gradient-to-r from-gray-100 to-gray-200 border-b border-gray-300;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .sidebar-logo-windows {
    @apply bg-gradient-to-br from-primary-500 to-primary-700 rounded-lg shadow-md;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  .sidebar-nav-item-windows {
    @apply relative flex items-center px-3 py-2.5 mx-2 text-sm font-medium rounded-md transition-all duration-200;
    background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
  }

  .sidebar-nav-item-windows:hover {
    @apply bg-gradient-to-r from-gray-200 to-gray-300 text-gray-800 shadow-sm;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .sidebar-nav-item-windows.active {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 shadow-md;
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.8), 0 2px 4px rgba(0, 0, 0, 0.1), inset 3px 0 0 #475569;
    transform: translateX(2px);
  }

  .sidebar-nav-item-windows.active:hover {
    transform: translateX(3px);
  }

  .sidebar-nav-item-windows.active::before {
    content: '';
    @apply absolute left-0 top-0 bottom-0 w-1 bg-primary-600 rounded-r-full;
    box-shadow: 0 0 4px rgba(71, 85, 105, 0.4);
  }

  .sidebar-nav-icon-windows {
    @apply mr-3 h-5 w-5 transition-all duration-200;
  }

  .sidebar-nav-item-windows:hover .sidebar-nav-icon-windows {
    @apply text-gray-700;
    transform: scale(1.05);
  }

  .sidebar-nav-item-windows.active .sidebar-nav-icon-windows {
    @apply text-primary-700;
  }

  .sidebar-section-divider {
    @apply mx-4 my-3 border-t border-gray-300 transition-opacity duration-300;
    box-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .sidebar-section-title {
    @apply px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider;
  }

  /* Windows-style focus and interaction states */
  .sidebar-nav-item-windows:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-gray-100;
  }

  .sidebar-nav-item-windows:active {
    transform: scale(0.98);
  }

  /* Enhanced logo with Windows-style depth */
  .sidebar-logo-windows:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.4);
  }

  /* Windows-style button effects for header */
  .sidebar-header-windows button:hover {
    @apply bg-gray-300;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* Windows-style text selection */
  .sidebar-windows ::selection {
    @apply bg-primary-200 text-primary-900;
  }

  /* Enhanced mobile sidebar overlay */
  .sidebar-mobile-overlay {
    backdrop-filter: blur(4px);
    background: rgba(75, 85, 99, 0.75);
  }

  /* Responsive Sidebar Adjustments */
  @media (max-width: 1199px) and (min-width: 1024px) {
    .sidebar-windows {
      width: 14rem !important; /* 224px - Reduced from default width */
    }
  }

  @media (max-width: 1023px) {
    .sidebar-windows {
      /* Mobile sidebar - not fixed positioned */
      position: relative !important;
      width: 100% !important;
      height: auto !important;
      top: auto !important;
      bottom: auto !important;
      left: auto !important;
      z-index: 50 !important;
    }

    .sidebar-nav-item {
      @apply px-3 py-2 text-sm;
    }
  }

  /* Desktop sidebar specific styles */
  @media (min-width: 1024px) {
    .sidebar-windows {
      width: 16rem !important; /* 256px - Default desktop width */
    }
  }

  /* Large screen sidebar adjustments */
  @media (min-width: 2560px) {
    .sidebar-windows {
      width: 18rem !important; /* 288px - Slightly wider for large screens */
    }
  }

  @media (min-width: 3840px) {
    .sidebar-windows {
      width: 20rem !important; /* 320px - Even wider for 4K screens */
    }
  }

  @media (min-width: 7680px) {
    .sidebar-windows {
      width: 24rem !important; /* 384px - Wider for 8K screens */
    }
  }

  /* Responsive Layout Utilities */
  .responsive-container {
    @apply w-full mx-auto px-4;
  }

  @media (min-width: 1024px) {
    .responsive-container {
      @apply px-6;
    }
  }

  @media (min-width: 1200px) {
    .responsive-container {
      @apply px-8;
    }
  }

  @media (min-width: 1440px) {
    .responsive-container {
      @apply px-10;
    }
  }

  @media (min-width: 1920px) {
    .responsive-container {
      @apply px-12;
    }
  }

  @media (min-width: 2560px) {
    .responsive-container {
      @apply px-16;
    }
  }

  @media (min-width: 3440px) {
    .responsive-container {
      @apply px-20;
    }
  }

  /* Responsive Grid System */
  .responsive-grid {
    @apply grid gap-4;
  }

  @media (max-width: 1023px) {
    .responsive-grid {
      @apply grid-cols-1 gap-3;
    }
  }

  @media (min-width: 1024px) and (max-width: 1199px) {
    .responsive-grid {
      @apply grid-cols-2 gap-4;
    }
  }

  @media (min-width: 1200px) and (max-width: 1439px) {
    .responsive-grid {
      @apply grid-cols-3 gap-6;
    }
  }

  @media (min-width: 1440px) and (max-width: 1919px) {
    .responsive-grid {
      @apply grid-cols-4 gap-6;
    }
  }

  @media (min-width: 1920px) and (max-width: 2559px) {
    .responsive-grid {
      @apply grid-cols-5 gap-8;
    }
  }

  @media (min-width: 2560px) and (max-width: 3439px) {
    .responsive-grid {
      @apply grid-cols-6 gap-8;
    }
  }

  @media (min-width: 3440px) and (max-width: 4095px) {
    .responsive-grid {
      @apply grid-cols-8 gap-10;
    }
  }

  @media (min-width: 4096px) and (max-width: 5119px) {
    .responsive-grid {
      @apply grid-cols-10 gap-12;
    }
  }

  @media (min-width: 5120px) and (max-width: 7679px) {
    .responsive-grid {
      @apply grid-cols-12 gap-14;
    }
  }

  @media (min-width: 7680px) {
    .responsive-grid {
      @apply grid-cols-16 gap-16;
    }
  }

  /* Responsive Text Scaling */
  .responsive-text-base {
    @apply text-sm;
  }

  @media (min-width: 1024px) {
    .responsive-text-base {
      @apply text-base;
    }
  }

  @media (min-width: 1920px) {
    .responsive-text-base {
      @apply text-lg;
    }
  }

  @media (min-width: 2560px) {
    .responsive-text-base {
      @apply text-xl;
    }
  }

  @media (min-width: 3440px) {
    .responsive-text-base {
      @apply text-2xl;
    }
  }

  @media (min-width: 4096px) {
    .responsive-text-base {
      @apply text-3xl;
    }
  }

  @media (min-width: 7680px) {
    .responsive-text-base {
      @apply text-4xl;
    }
  }

  /* Responsive Spacing */
  .responsive-spacing {
    @apply p-3;
  }

  @media (min-width: 1024px) {
    .responsive-spacing {
      @apply p-4;
    }
  }

  @media (min-width: 1200px) {
    .responsive-spacing {
      @apply p-6;
    }
  }

  @media (min-width: 1920px) {
    .responsive-spacing {
      @apply p-8;
    }
  }

  @media (min-width: 2560px) {
    .responsive-spacing {
      @apply p-10;
    }
  }

  @media (min-width: 3440px) {
    .responsive-spacing {
      @apply p-12;
    }
  }

  @media (min-width: 4096px) {
    .responsive-spacing {
      @apply p-16;
    }
  }

  @media (min-width: 7680px) {
    .responsive-spacing {
      @apply p-20;
    }
  }

  /* Ultra-wide Screen Optimizations */
  @media (min-width: 2560px) {
    .card {
      @apply text-lg;
    }

    .card-header {
      @apply px-8 py-6;
    }

    .card-body {
      @apply px-8 py-6;
    }

    .btn {
      @apply px-6 py-3 text-lg;
    }

    .btn-lg {
      @apply px-10 py-5 text-xl;
    }
  }

  /* Extreme Large Screen Optimizations (4K and above) */
  @media (min-width: 3840px) {
    .card {
      @apply text-xl;
    }

    .card-header {
      @apply px-12 py-8;
    }

    .card-body {
      @apply px-12 py-8;
    }

    .btn {
      @apply px-8 py-4 text-xl;
    }

    .btn-lg {
      @apply px-12 py-6 text-2xl;
    }

    .table-modern {
      @apply text-lg;
    }

    .table-header-modern th {
      @apply px-10 py-5 text-lg;
    }

    .table-body-modern td {
      @apply px-10 py-5 text-lg;
    }
  }

  /* Ensure app content doesn't become too wide on extremely large screens */
  @media (min-width: 5120px) {
    .responsive-container {
      max-width: 4800px !important;
      margin: 0 auto !important;
    }
  }

  /* Ensure proper scaling for 8K displays */
  @media (min-width: 7680px) {
    .responsive-container {
      max-width: 7200px !important;
      margin: 0 auto !important;
    }

    .card {
      @apply text-2xl;
    }

    .btn {
      @apply px-10 py-5 text-2xl;
    }

    .btn-lg {
      @apply px-16 py-8 text-3xl;
    }
  }

  @media (min-width: 3440px) {
    .card {
      @apply text-xl;
    }

    .card-header {
      @apply px-10 py-8;
    }

    .card-body {
      @apply px-10 py-8;
    }

    .btn {
      @apply px-8 py-4 text-xl;
    }

    .btn-lg {
      @apply px-12 py-6 text-2xl;
    }
  }

  /* Windows-Style Main Layout Components */
  .windows-header {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8),
      inset 0 -1px 0 rgba(0, 0, 0, 0.05);
  }

  .windows-button {
    background: linear-gradient(180deg, #f3f4f6 0%, #e5e7eb 100%);
    border: 1px solid #d1d5db;
    box-shadow:
      0 1px 2px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .windows-button:hover {
    background: linear-gradient(180deg, #e5e7eb 0%, #d1d5db 100%);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
  }

  .windows-button:active {
    transform: translateY(0);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05);
  }

  .windows-user-info {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
  }

  .windows-logout-button {
    background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.2s ease-out;
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  }

  .windows-logout-button:hover {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow:
      0 2px 6px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(-1px);
  }

  .windows-logout-button:active {
    transform: translateY(0);
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(0, 0, 0, 0.05);
  }

  .windows-main-content {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    /* Ensure main content scrolls independently of sidebar */
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .windows-app-background {
    background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    /* Prevent body scrolling to ensure only main content scrolls */
    height: 100vh;
    overflow: hidden;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
  box-shadow: inset 0 0 2px rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #94a3b8 0%, #64748b 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, #64748b 0%, #475569 100%);
}

/* Windows-style scrollbar for sidebar */
.sidebar-windows::-webkit-scrollbar {
  width: 10px;
}

.sidebar-windows::-webkit-scrollbar-track {
  background: #f8fafc;
  border-left: 1px solid #e2e8f0;
}

.sidebar-windows::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #d1d5db 0%, #9ca3af 100%);
  border-radius: 5px;
  border: 1px solid #e5e7eb;
}

.sidebar-windows::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #9ca3af 0%, #6b7280 100%);
}

/* Enhanced scrollbar for main content */
.windows-main-content::-webkit-scrollbar {
  width: 12px;
}

.windows-main-content::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 6px;
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.1);
}

.windows-main-content::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.windows-main-content::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #94a3b8 0%, #64748b 100%);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.windows-main-content::-webkit-scrollbar-thumb:active {
  background: linear-gradient(180deg, #64748b 0%, #475569 100%);
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-before: always;
  }

  body {
    @apply text-black bg-white;
  }
}
