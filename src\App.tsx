import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import ErrorBoundary from './components/ErrorBoundary'
import Layout from './components/Layout'
import { initializeGlobalErrorHandler } from './utils/globalErrorHandler'
import Dashboard from './pages/Dashboard'
import Books from './pages/Books'
import BookForm from './pages/BookForm'
import BookDetail from './pages/BookDetail'
import Members from './pages/Members'
import MemberForm from './pages/MemberForm'
import MemberDetail from './pages/MemberDetail'
import Borrowings from './pages/Borrowings'
import BorrowingForm from './pages/BorrowingForm'
import BorrowingDetail from './pages/BorrowingDetail'
import Authors from './pages/Authors'
import AuthorForm from './pages/AuthorForm'
import AuthorDetail from './pages/AuthorDetail'
import Categories from './pages/Categories'
import CategoryForm from './pages/CategoryForm'
import CategoryDetail from './pages/CategoryDetail'
import Sources from './pages/Sources'
import SourceForm from './pages/SourceForm'
import SourceDetail from './pages/SourceDetail'
import Publishers from './pages/Publishers'
import PublisherForm from './pages/PublisherForm'
import PublisherDetail from './pages/PublisherDetail'
import Languages from './pages/Languages'
import LanguageForm from './pages/LanguageForm'
import LanguageDetail from './pages/LanguageDetail'
import BookSeries from './pages/BookSeries'
import BookSeriesForm from './pages/BookSeriesForm'
import BookSeriesDetail from './pages/BookSeriesDetail'
import Subjects from './pages/Subjects'
import SubjectForm from './pages/SubjectForm'
import SubjectDetail from './pages/SubjectDetail'
import Locations from './pages/Locations'
import LocationForm from './pages/LocationForm'
import LocationDetail from './pages/LocationDetail'
import Conditions from './pages/Conditions'
import ConditionForm from './pages/ConditionForm'
import ConditionDetail from './pages/ConditionDetail'

import UserLogs from './pages/UserLogs'
import ImportBooks from './pages/ImportBooks'
import EmailSettings from './pages/EmailSettings'
import EmailManagement from './pages/EmailManagement'
import BackupRestore from './pages/BackupRestore'
import UserManagement from './pages/UserManagement'
import SoftDeleteManagement from './pages/SoftDeleteManagement'
import Login from './pages/Login'
import { useAuthStore } from './store/authStore'
import { withPermission, withRole } from './hooks/usePermissions'

// Create permission-protected components
const ProtectedBooks = withPermission(Books, 'canManageBooks')
const ProtectedBookForm = withPermission(BookForm, 'canManageBooks')
const ProtectedBookDetail = withPermission(BookDetail, 'canManageBooks')
const ProtectedMembers = withPermission(Members, 'canManageMembers')
const ProtectedMemberForm = withPermission(MemberForm, 'canManageMembers')
const ProtectedMemberDetail = withPermission(MemberDetail, 'canManageMembers')
const ProtectedBorrowings = withPermission(Borrowings, 'canManageBorrowings')
const ProtectedBorrowingForm = withPermission(BorrowingForm, 'canManageBorrowings')
const ProtectedBorrowingDetail = withPermission(BorrowingDetail, 'canManageBorrowings')
const ProtectedAuthors = withPermission(Authors, 'canManageCategories')
const ProtectedAuthorForm = withPermission(AuthorForm, 'canManageCategories')
const ProtectedAuthorDetail = withPermission(AuthorDetail, 'canManageCategories')
const ProtectedCategories = withPermission(Categories, 'canManageCategories')
const ProtectedCategoryForm = withPermission(CategoryForm, 'canManageCategories')
const ProtectedCategoryDetail = withPermission(CategoryDetail, 'canManageCategories')
const ProtectedSources = withPermission(Sources, 'canManageSources')
const ProtectedSourceForm = withPermission(SourceForm, 'canManageSources')
const ProtectedSourceDetail = withPermission(SourceDetail, 'canManageSources')
const ProtectedPublishers = withPermission(Publishers, 'canManageCategories')
const ProtectedPublisherForm = withPermission(PublisherForm, 'canManageCategories')
const ProtectedPublisherDetail = withPermission(PublisherDetail, 'canManageCategories')
const ProtectedLanguages = withPermission(Languages, 'canManageCategories')
const ProtectedLanguageForm = withPermission(LanguageForm, 'canManageCategories')
const ProtectedLanguageDetail = withPermission(LanguageDetail, 'canManageCategories')
const ProtectedBookSeries = withPermission(BookSeries, 'canManageCategories')
const ProtectedBookSeriesForm = withPermission(BookSeriesForm, 'canManageCategories')
const ProtectedBookSeriesDetail = withPermission(BookSeriesDetail, 'canManageCategories')
const ProtectedSubjects = withPermission(Subjects, 'canManageCategories')
const ProtectedSubjectForm = withPermission(SubjectForm, 'canManageCategories')
const ProtectedSubjectDetail = withPermission(SubjectDetail, 'canManageCategories')
const ProtectedLocations = withPermission(Locations, 'canManageCategories')
const ProtectedLocationForm = withPermission(LocationForm, 'canManageCategories')
const ProtectedLocationDetail = withPermission(LocationDetail, 'canManageCategories')
const ProtectedConditions = withPermission(Conditions, 'canManageCategories')
const ProtectedConditionForm = withPermission(ConditionForm, 'canManageCategories')
const ProtectedConditionDetail = withPermission(ConditionDetail, 'canManageCategories')

const ProtectedUserLogs = withPermission(UserLogs, 'canViewLogs')
const ProtectedImportBooks = withPermission(ImportBooks, 'canImportData')
const ProtectedEmailSettings = withPermission(EmailSettings, 'canManageSystem')
const ProtectedEmailManagement = withPermission(EmailManagement, 'canManageSystem')
const ProtectedBackupRestore = withPermission(BackupRestore, 'canManageSystem')
const ProtectedUserManagement = withPermission(UserManagement, 'canManageUsers')
const ProtectedSoftDeleteManagement = withRole(SoftDeleteManagement, 'ADMIN')

function App() {
  const { isAuthenticated, isLoading, restoreSession } = useAuthStore()
  const [isInitializing, setIsInitializing] = React.useState(true)

  // Initialize global error handler and restore session
  React.useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize global error handler
        initializeGlobalErrorHandler();

        // Try to restore session from previous app session
        console.log('🔄 Attempting to restore session...')
        await restoreSession()

      } catch (error) {
        console.error('App initialization error:', error)
      } finally {
        setIsInitializing(false)
      }
    }

    initializeApp()
  }, [restoreSession]);

  // Show loading screen during initialization
  if (isInitializing || isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">
            {isInitializing ? 'Initializing application...' : 'Restoring session...'}
          </p>
          <p className="text-sm text-gray-500 mt-2">
            {isInitializing ? 'एप्लिकेशन प्रारम्भ कऽ रहल अछि...' : 'सत्र पुनर्स्थापना कऽ रहल अछि...'}
          </p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return <Login key="login-form" />
  }

  return (
    <ErrorBoundary>
      <Layout>
        <Routes>
          <Route path="/" element={<Dashboard />} />
          <Route path="/dashboard" element={<Dashboard />} />

        {/* Books Routes */}
        <Route path="/books" element={<ProtectedBooks />} />
        <Route path="/books/new" element={<ProtectedBookForm />} />
        <Route path="/books/import" element={<ProtectedImportBooks />} />
        <Route path="/books/:id" element={<ProtectedBookDetail />} />
        <Route path="/books/:id/edit" element={<ProtectedBookForm />} />

        {/* Members Routes */}
        <Route path="/members" element={<ProtectedMembers />} />
        <Route path="/members/new" element={<ProtectedMemberForm />} />
        <Route path="/members/:id" element={<ProtectedMemberDetail />} />
        <Route path="/members/:id/edit" element={<ProtectedMemberForm />} />

        {/* Borrowings Routes */}
        <Route path="/borrowings" element={<ProtectedBorrowings />} />
        <Route path="/borrowings/new" element={<ProtectedBorrowingForm />} />
        <Route path="/borrowings/:id" element={<ProtectedBorrowingDetail />} />

        {/* Authors Routes */}
        <Route path="/authors" element={<ProtectedAuthors />} />
        <Route path="/authors/new" element={<ProtectedAuthorForm />} />
        <Route path="/authors/:id" element={<ProtectedAuthorDetail />} />
        <Route path="/authors/:id/edit" element={<ProtectedAuthorForm />} />

        {/* Categories Routes */}
        <Route path="/categories" element={<ProtectedCategories />} />
        <Route path="/categories/new" element={<ProtectedCategoryForm />} />
        <Route path="/categories/:id" element={<ProtectedCategoryDetail />} />
        <Route path="/categories/:id/edit" element={<ProtectedCategoryForm />} />

        {/* Sources Routes */}
        <Route path="/sources" element={<ProtectedSources />} />
        <Route path="/sources/new" element={<ProtectedSourceForm />} />
        <Route path="/sources/:id" element={<ProtectedSourceDetail />} />
        <Route path="/sources/:id/edit" element={<ProtectedSourceForm />} />

        {/* Publishers Routes */}
        <Route path="/publishers" element={<ProtectedPublishers />} />
        <Route path="/publishers/new" element={<ProtectedPublisherForm />} />
        <Route path="/publishers/:id" element={<ProtectedPublisherDetail />} />
        <Route path="/publishers/:id/edit" element={<ProtectedPublisherForm />} />

        {/* Languages Routes */}
        <Route path="/languages" element={<ProtectedLanguages />} />
        <Route path="/languages/new" element={<ProtectedLanguageForm />} />
        <Route path="/languages/:id" element={<ProtectedLanguageDetail />} />
        <Route path="/languages/:id/edit" element={<ProtectedLanguageForm />} />

        {/* Book Series Routes */}
        <Route path="/book-series" element={<ProtectedBookSeries />} />
        <Route path="/book-series/new" element={<ProtectedBookSeriesForm />} />
        <Route path="/book-series/:id" element={<ProtectedBookSeriesDetail />} />
        <Route path="/book-series/:id/edit" element={<ProtectedBookSeriesForm />} />

        {/* Subjects Routes */}
        <Route path="/subjects" element={<ProtectedSubjects />} />
        <Route path="/subjects/new" element={<ProtectedSubjectForm />} />
        <Route path="/subjects/:id" element={<ProtectedSubjectDetail />} />
        <Route path="/subjects/:id/edit" element={<ProtectedSubjectForm />} />

        {/* Locations Routes */}
        <Route path="/locations" element={<ProtectedLocations />} />
        <Route path="/locations/new" element={<ProtectedLocationForm />} />
        <Route path="/locations/:id" element={<ProtectedLocationDetail />} />
        <Route path="/locations/:id/edit" element={<ProtectedLocationForm />} />

        {/* Conditions Routes */}
        <Route path="/conditions" element={<ProtectedConditions />} />
        <Route path="/conditions/new" element={<ProtectedConditionForm />} />
        <Route path="/conditions/:id" element={<ProtectedConditionDetail />} />
        <Route path="/conditions/:id/edit" element={<ProtectedConditionForm />} />



        {/* System Routes */}
        <Route path="/user-logs" element={<ProtectedUserLogs />} />
        <Route path="/user-management" element={<ProtectedUserManagement />} />
        <Route path="/email-settings" element={<ProtectedEmailSettings />} />
        <Route path="/email-management" element={<ProtectedEmailManagement />} />
        <Route path="/backup-restore" element={<ProtectedBackupRestore />} />
        <Route path="/soft-delete-management" element={<ProtectedSoftDeleteManagement />} />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </Layout>
    </ErrorBoundary>
  )
}

export default App
