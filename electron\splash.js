const { BrowserWindow, nativeImage, app } = require('electron');
const path = require('path');

class SplashScreen {
  constructor(options = {}) {
    this.splashWindow = null;
    this.isDev = options.isDev || false;
    this.pendingMessages = [];
    this.isReady = false;
  }

  create() {
    try {
      // Check if app is available and ready
      if (app && typeof app.isReady === 'function' && !app.isReady()) {
        console.log('⏳ App not ready, waiting to create splash screen...');
        app.whenReady().then(() => {
          this._createWindow();
        });
        return;
      }

      this._createWindow();
    } catch (error) {
      console.warn('⚠️ Could not create splash screen immediately:', error.message);
      // Fallback: wait for app to be ready
      if (app && typeof app.whenReady === 'function') {
        app.whenReady().then(() => {
          this._createWindow();
        });
      }
    }
  }

  _createWindow() {
    try {
      // Create splash screen window
      this.splashWindow = new BrowserWindow({
        width: 500,
        height: 400,
        frame: false,
        alwaysOnTop: true,
        transparent: true,
        resizable: false,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true
        },
        icon: path.join(__dirname, '../assets/icon.png'),
        show: false
      });

      console.log('✅ Splash screen window created successfully');
    } catch (error) {
      console.error('❌ Failed to create splash screen window:', error);
      return;
    }

    // Create splash screen HTML content
    const splashHTML = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          height: 100vh;
          color: white;
          overflow: hidden;
          border-radius: 12px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
        
        .logo {
          width: 80px;
          height: 80px;
          background: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 20px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.2);
          animation: pulse 2s infinite;
        }
        
        .logo-text {
          font-size: 24px;
          font-weight: bold;
          color: #667eea;
        }
        
        .organization-name {
          font-size: 24px;
          font-weight: 600;
          margin-bottom: 4px;
          text-align: center;
          text-shadow: 0 2px 4px rgba(0,0,0,0.3);
          animation: fade 3s infinite;
        }

        .organization-subtitle {
          font-size: 14px;
          opacity: 0.8;
          margin-bottom: 16px;
          text-align: center;
          font-weight: 300;
          font-style: italic;
        }

        .library-name {
          font-size: 18px;
          font-weight: 500;
          margin-bottom: 4px;
          text-align: center;
          text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .library-subtitle {
          font-size: 13px;
          opacity: 0.8;
          margin-bottom: 30px;
          text-align: center;
          font-weight: 300;
          font-style: italic;
        }
        
        .loading-container {
          width: 200px;
          height: 4px;
          background: rgba(255,255,255,0.3);
          border-radius: 2px;
          overflow: hidden;
          margin-bottom: 20px;
        }
        
        .loading-bar {
          height: 100%;
          background: linear-gradient(90deg, #fff, #f0f0f0);
          border-radius: 2px;
          animation: loading 2s infinite;
        }
        
        .loading-text {
          font-size: 12px;
          opacity: 0.8;
          animation: fade 1.5s infinite;
        }
        
        .developer-info {
          position: absolute;
          bottom: 20px;
          text-align: center;
          font-size: 11px;
          opacity: 0.7;
        }
        
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        
        @keyframes loading {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(200px); }
        }
        
        @keyframes fade {
          0%, 100% { opacity: 0.8; }
          50% { opacity: 1; }
        }
      </style>
    </head>
    <body>
      <div class="logo">
        <div class="logo-text">📚</div>
      </div>

      <div class="organization-name">मैथिली विकास कोष</div>
      <div class="organization-subtitle">Maithili Vikas Kosh</div>

      <div class="library-name">विद्यापति पुस्तकालय - अध्ययन तथा अनुसंधान केन्द्र</div>
      <div class="library-subtitle">Vidyapati Library - Study and Research Center</div>
      
      <div class="loading-container">
        <div class="loading-bar"></div>
      </div>
      
      <div class="loading-text" id="loadingText">एप्लिकेशन प्रारंभ कऽ रहल अछि...</div>
      
      <div class="developer-info">
        Developed by श्रद्धेय वात्स्यायन<br>
        © 2025 All rights reserved
        ${this.isDev ? '<br><span style="color: #ffd700; font-weight: bold;">🔧 DEVELOPMENT MODE</span>' : ''}
      </div>
      
      <script>
        const loadingTexts = [
          'एप्लिकेशन प्रारंभ कऽ रहल अछि...',
          'पुस्तकालय सिस्टम लोड कऽ रहल अछि...',
          'डेटाबेस सँ जुड़ि रहल अछि...',
          'ईमेल सिस्टम जांच कऽ रहल अछि...',
          'बैकअप सिस्टम जांच कऽ रहल अछि...',
          'यूजर इंटरफेस तैयार कऽ रहल अछि...',
          'लगभग तैयार अछि...'
        ];

        let currentIndex = 0;
        const loadingTextElement = document.getElementById('loadingText');

        // Listen for progress updates from main process
        window.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'startup-progress') {
            loadingTextElement.textContent = event.data.message;
          }
        });

        // Fallback rotation if no progress updates
        const rotationInterval = setInterval(() => {
          currentIndex = (currentIndex + 1) % loadingTexts.length;
          loadingTextElement.textContent = loadingTexts[currentIndex];
        }, 1200);

        // Clear interval when we get progress updates
        window.addEventListener('message', (event) => {
          if (event.data && event.data.type === 'startup-progress') {
            clearInterval(rotationInterval);
          }
        });
      </script>
    </body>
    </html>
    `;

    // Load the splash screen content
    this.splashWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(splashHTML)}`);

    // Show splash screen
    this.splashWindow.once('ready-to-show', () => {
      this.splashWindow.show();
      this.isReady = true;
      console.log('🎉 Splash screen is now visible');

      // Process any pending messages
      if (this.pendingMessages.length > 0) {
        console.log(`📝 Processing ${this.pendingMessages.length} pending messages`);
        this.pendingMessages.forEach(message => {
          this._updateProgressInternal(message);
        });
        this.pendingMessages = [];
      }
    });

    return this.splashWindow;
  }

  updateProgress(message) {
    // If splash screen is not ready yet, queue the message
    if (!this.isReady || !this.splashWindow || this.splashWindow.isDestroyed()) {
      console.log(`📋 Queuing message: ${message}`);
      this.pendingMessages.push(message);
      return;
    }

    this._updateProgressInternal(message);
  }

  _updateProgressInternal(message) {
    if (this.splashWindow && !this.splashWindow.isDestroyed()) {
      this.splashWindow.webContents.executeJavaScript(`
        const element = document.getElementById('loadingText');
        if (element) {
          element.textContent = '${message}';
        }
      `).catch(() => {
        // Ignore errors if window is closing
      });
    }
  }

  destroy() {
    if (this.splashWindow) {
      this.splashWindow.close();
      this.splashWindow = null;
    }
  }

  isVisible() {
    return this.splashWindow && this.splashWindow.isVisible();
  }
}

module.exports = SplashScreen;
