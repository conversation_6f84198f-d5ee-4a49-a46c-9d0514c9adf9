import React, { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom'
import { toast } from 'react-toastify'
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  BookOpenIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ChartBarIcon,
  ArrowsRightLeftIcon,
  DocumentIcon,
  MagnifyingGlassIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  ChevronUpDownIcon
} from '@heroicons/react/24/outline'
import SourceMergeDialog from '../components/SourceMergeDialog'
import Pagination from '../components/Pagination'

interface Source {
  id: string
  name: string
  nameNepali?: string
  type: string
  contactPerson?: string
  email?: string
  phone?: string
  address?: string
  description?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  stats: {
    totalBooks: number
    totalValue: number
    availableBooks: number
    borrowedBooks: number
    firstDonation?: string
    lastDonation?: string
    averageBookValue: number
    booksByCategory: Record<string, number>
    booksByLanguage: Record<string, number>
    recentBooks: Array<any>
  }
}

interface Book {
  id: string
  title: string
  titleNepali?: string
  accessionNo: string
  author: {
    name: string
    nameNepali?: string
  }
  category: {
    name: string
    nameNepali?: string
  }
  language: {
    name: string
    nameNepali?: string
  }
  price?: number
  dateReceived?: string
  isAvailable: boolean
  borrowings: Array<{
    member: {
      name: string
      memberNo: string
    }
  }>
}

interface Pagination {
  page: number
  limit: number
  total: number
  pages: number
}

const sourceTypeLabels: Record<string, string> = {
  DONOR: 'Donor (दाता)',
  PURCHASE: 'Purchase (खरिद)',
  GOVERNMENT: 'Government (सरकार)',
  ORGANIZATION: 'Organization (संस्था)',
  INDIVIDUAL: 'Individual (व्यक्तिगत)',
  OTHER: 'Other (अन्य)'
}

export default function SourceDetail() {
  const { id } = useParams()
  const navigate = useNavigate()
  const [source, setSource] = useState<Source | null>(null)
  const [loading, setLoading] = useState(true)
  const [showMergeDialog, setShowMergeDialog] = useState(false)

  // Books pagination state
  const [books, setBooks] = useState<Book[]>([])
  const [booksLoading, setBooksLoading] = useState(false)
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [search, setSearch] = useState('')
  const [sortBy, setSortBy] = useState('dateReceived')
  const [sortOrder, setSortOrder] = useState('desc')

  useEffect(() => {
    if (id) {
      fetchSource()
    }
  }, [id])

  useEffect(() => {
    if (id) {
      fetchBooks()
    }
  }, [id, pagination.page, search, sortBy, sortOrder])

  const fetchSource = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/sources/${id}`)
      if (response.ok) {
        const data = await response.json()
        setSource(data)
      } else {
        toast.error('Failed to fetch source details')
        navigate('/sources')
      }
    } catch (error) {
      console.error('Error fetching source:', error)
      toast.error('Failed to fetch source details')
      navigate('/sources')
    } finally {
      setLoading(false)
    }
  }

  const fetchBooks = async () => {
    if (!id) return

    setBooksLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search,
        sortBy,
        sortOrder
      })

      const response = await fetch(`/api/sources/${id}/books?${params}`)
      if (response.ok) {
        const data = await response.json()
        setBooks(data.books)
        setPagination(data.pagination)
      } else {
        toast.error('Failed to fetch books from source')
      }
    } catch (error) {
      console.error('Error fetching books from source:', error)
      toast.error('Failed to fetch books from source')
    } finally {
      setBooksLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!source || !confirm(`Are you sure you want to delete source "${source.name}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/sources/${id}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('Source deleted successfully')
        navigate('/sources')
      } else {
        const error = await response.json()
        toast.error(error.error || 'Failed to delete source')
      }
    } catch (error) {
      console.error('Error deleting source:', error)
      toast.error('Failed to delete source')
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'NPR',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-IN')
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
  }

  const handleSearchChange = (newSearch: string) => {
    setSearch(newSearch)
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
    setPagination(prev => ({ ...prev, page: 1 }))
  }

  const getSortIcon = (field: string) => {
    if (sortBy !== field) {
      return <ChevronUpDownIcon className="w-4 h-4 text-gray-400" />
    }
    return sortOrder === 'asc'
      ? <ChevronUpIcon className="w-4 h-4 text-primary-600" />
      : <ChevronDownIcon className="w-4 h-4 text-primary-600" />
  }

  const renderSortableHeader = (field: string, label: string) => (
    <th
      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none"
      onClick={() => handleSort(field)}
    >
      <div className="flex items-center justify-between">
        <span>{label}</span>
        {getSortIcon(field)}
      </div>
    </th>
  )

  const handleDownloadSourceBooksPDF = async () => {
    if (!source || source.stats.totalBooks === 0) {
      toast.error('No books available to download')
      return
    }

    try {
      toast.info('Generating source books PDF... This may take a moment.', { autoClose: 3000 })

      const response = await fetch(`/api/pdf/source/${source.id}/books`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to generate source books PDF')
      }

      // Get the PDF blob
      const blob = await response.blob()

      // Create download link
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // Generate filename with source name and current date
      const now = new Date()
      const timestamp = now.toISOString().split('T')[0] // YYYY-MM-DD format
      const sanitizedSourceName = source.name.replace(/[^a-zA-Z0-9\s]/g, '').replace(/\s+/g, '-').toLowerCase()
      link.download = `source-${sanitizedSourceName}-books-${timestamp}.pdf`

      // Trigger download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // Clean up
      window.URL.revokeObjectURL(url)

      toast.success('Source books PDF downloaded successfully!')

    } catch (error) {
      console.error('Error downloading source books PDF:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to download source books PDF')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    )
  }

  if (!source) {
    return (
      <div className="text-center py-12">
        <h3 className="mt-2 text-sm font-medium text-gray-900">Source not found</h3>
        <p className="mt-1 text-sm text-gray-500">
          The source you're looking for doesn't exist.
        </p>
        <div className="mt-6">
          <Link to="/sources" className="btn btn-primary">
            Back to Sources
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/sources')}
            className="btn btn-secondary btn-sm"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Sources
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{source?.name || 'Unknown Source'}</h1>
            {source.nameNepali && (
              <p className="text-lg text-gray-600 text-nepali">{source.nameNepali}</p>
            )}
            <p className="text-sm text-gray-500">
              {sourceTypeLabels[source.type] || source.type}
            </p>
          </div>
        </div>
        <div className="flex space-x-2">
          <Link
            to={`/sources/${source.id}/edit`}
            className="btn btn-secondary"
          >
            <PencilIcon className="w-4 h-4 mr-2" />
            Edit
          </Link>
          <button
            onClick={() => setShowMergeDialog(true)}
            className="btn btn-secondary"
          >
            <ArrowsRightLeftIcon className="w-4 h-4 mr-2" />
            Merge
          </button>
          <button
            onClick={handleDelete}
            className="btn btn-danger"
          >
            <TrashIcon className="w-4 h-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* Status Badge */}
      <div>
        <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
          source.isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {source.isActive ? 'Active' : 'Inactive'}
        </span>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <BookOpenIcon className="w-8 h-8 text-primary-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Books</p>
                <p className="text-2xl font-bold text-gray-900">{source.stats.totalBooks}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="w-8 h-8 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(source.stats.totalValue)}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="w-8 h-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Available</p>
                <p className="text-2xl font-bold text-gray-900">{source.stats.availableBooks}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-body">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="w-8 h-8 text-orange-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Last Donation</p>
                <p className="text-lg font-bold text-gray-900">
                  {formatDate(source.stats.lastDonation)}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Source Information */}
        <div className="lg:col-span-1">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">Source Information</h3>
            </div>
            <div className="card-body space-y-4">
              {source.contactPerson && (
                <div className="flex items-center">
                  <UserIcon className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Contact Person</p>
                    <p className="text-sm text-gray-600">{source.contactPerson}</p>
                  </div>
                </div>
              )}

              {source.phone && (
                <div className="flex items-center">
                  <PhoneIcon className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Phone</p>
                    <p className="text-sm text-gray-600">{source.phone}</p>
                  </div>
                </div>
              )}

              {source.email && (
                <div className="flex items-center">
                  <EnvelopeIcon className="w-5 h-5 text-gray-400 mr-3" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email</p>
                    <p className="text-sm text-gray-600">{source.email}</p>
                  </div>
                </div>
              )}

              {source.address && (
                <div className="flex items-start">
                  <MapPinIcon className="w-5 h-5 text-gray-400 mr-3 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Address</p>
                    <p className="text-sm text-gray-600">{source.address}</p>
                  </div>
                </div>
              )}

              {source.description && (
                <div>
                  <p className="text-sm font-medium text-gray-900 mb-2">Description</p>
                  <p className="text-sm text-gray-600">{source.description}</p>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="font-medium text-gray-900">First Donation</p>
                    <p className="text-gray-600">{formatDate(source.stats.firstDonation)}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">Average Value</p>
                    <p className="text-gray-600">{formatCurrency(source.stats.averageBookValue)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Category Distribution */}
          {Object.keys(source.stats.booksByCategory).length > 0 && (
            <div className="card mt-6">
              <div className="card-header">
                <h3 className="text-lg font-semibold text-gray-900">Books by Category</h3>
              </div>
              <div className="card-body">
                <div className="space-y-3">
                  {Object.entries(source.stats.booksByCategory)
                    .sort(([,a], [,b]) => b - a)
                    .map(([category, count]) => (
                    <div key={category} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{category}</span>
                      <span className="text-sm font-medium text-gray-900">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Books List */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Books from this Source ({source.stats.totalBooks})
                </h3>
                {source.stats.totalBooks > 0 && (
                  <button
                    onClick={handleDownloadSourceBooksPDF}
                    className="btn btn-secondary btn-sm"
                    title="Download all books from this source as PDF"
                  >
                    <DocumentIcon className="w-4 h-4 mr-2" />
                    Download PDF
                  </button>
                )}
              </div>

              {/* Search and Sort Controls */}
              {source.stats.totalBooks > 0 && (
                <div className="mt-4 flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search books by title, author, accession number..."
                        className="input pl-10"
                        value={search}
                        onChange={(e) => handleSearchChange(e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <select
                      className="input w-auto"
                      value={`${sortBy}-${sortOrder}`}
                      onChange={(e) => {
                        const [field, order] = e.target.value.split('-')
                        setSortBy(field)
                        setSortOrder(order)
                        setPagination(prev => ({ ...prev, page: 1 }))
                      }}
                    >
                      <option value="dateReceived-desc">Date Received (Newest)</option>
                      <option value="dateReceived-asc">Date Received (Oldest)</option>
                      <option value="accessionNo-asc">Accession No. (A-Z)</option>
                      <option value="accessionNo-desc">Accession No. (Z-A)</option>
                      <option value="title-asc">Title (A-Z)</option>
                      <option value="title-desc">Title (Z-A)</option>
                      <option value="price-desc">Price (High-Low)</option>
                      <option value="price-asc">Price (Low-High)</option>
                    </select>
                  </div>
                </div>
              )}
            </div>
            <div className="card-body p-0">
              {booksLoading ? (
                <div className="flex items-center justify-center py-12">
                  <div className="spinner spinner-lg" />
                </div>
              ) : books.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpenIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    {search ? 'No books found' : 'No books found'}
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {search
                      ? `No books match your search "${search}"`
                      : 'No books have been added from this source yet.'
                    }
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="table-modern">
                    <thead className="table-header-modern">
                      <tr>
                        {renderSortableHeader('accessionNo', 'Accession No.')}
                        {renderSortableHeader('title', 'Title')}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Author
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Category
                        </th>
                        {renderSortableHeader('price', 'Price')}
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        {renderSortableHeader('dateReceived', 'Date Received')}
                      </tr>
                    </thead>
                    <tbody className="table-body-modern">
                      {books.map((book) => (
                        <tr
                          key={book.id}
                          className="table-row-hover"
                          onClick={() => navigate(`/books/${book.id}`)}
                        >
                          <td className="font-medium">{book.accessionNo}</td>
                          <td>
                            <div>
                              <p className="font-medium text-gray-900">{book.title}</p>
                              {book.titleNepali && (
                                <p className="text-sm text-gray-500 text-nepali">{book.titleNepali}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            <div>
                              <p className="text-gray-900">{book.author?.name || 'Unknown Author'}</p>
                              {book.author?.nameNepali && (
                                <p className="text-sm text-gray-500 text-nepali">{book.author.nameNepali}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            <div>
                              <p className="text-gray-900">{book.category?.name || 'Unknown Category'}</p>
                              {book.category?.nameNepali && (
                                <p className="text-sm text-gray-500 text-nepali">{book.category.nameNepali}</p>
                              )}
                            </div>
                          </td>
                          <td>
                            {book.price ? (
                              <div>
                                <p className="text-gray-900">Rs. {book.price.toLocaleString()}</p>
                              </div>
                            ) : (
                              <span className="text-gray-400">-</span>
                            )}
                          </td>
                          <td>
                            {book.isAvailable ? (
                              <span className="badge badge-success">Available</span>
                            ) : (
                              <div>
                                <span className="badge badge-warning">Borrowed</span>
                                {book.borrowings.length > 0 && (
                                  <p className="text-xs text-gray-500 mt-1">
                                    by {book.borrowings[0].member.name}
                                  </p>
                                )}
                              </div>
                            )}
                          </td>
                          <td>{formatDate(book.dateReceived)}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>

          {/* Pagination */}
          {books.length > 0 && (
            <div className="mt-6">
              <Pagination
                pagination={pagination}
                onPageChange={handlePageChange}
                itemName="books"
              />
            </div>
          )}
        </div>
      </div>

      {/* Source Merge Dialog */}
      <SourceMergeDialog
        isOpen={showMergeDialog}
        onClose={() => setShowMergeDialog(false)}
        onMergeComplete={() => {
          navigate('/sources')
        }}
        initialPrimarySource={source}
      />
    </div>
  )
}
