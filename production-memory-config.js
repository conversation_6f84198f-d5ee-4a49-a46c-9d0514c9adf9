/**
 * Production Memory Configuration
 * Optimizes memory usage for production deployment
 */

const fs = require('fs');
const path = require('path');

class ProductionMemoryConfig {
  constructor() {
    this.isProduction = process.env.NODE_ENV === 'production';
    this.maxMemoryMB = parseInt(process.env.MAX_MEMORY_MB) || (this.isProduction ? 1024 : 2048);
    this.gcEnabled = process.env.ENABLE_GC_MONITORING === 'true';

    // Treat development as production if high memory limit is set
    this.treatDevAsProduction = this.maxMemoryMB >= 2048;
    this.isProductionLevel = this.isProduction || this.treatDevAsProduction;

    this.setupMemoryOptimizations();
  }

  setupMemoryOptimizations() {
    if (this.isProductionLevel) {
      const mode = this.isProduction ? 'Production' : 'Development (Production-level)';
      console.log(`🏭 ${mode} memory optimizations enabled`);
      console.log(`   • Memory limit: ${this.maxMemoryMB}MB`);
      console.log(`   • Garbage collection: ${global.gc ? 'Enabled' : 'Disabled (--expose-gc needed)'}`);
      console.log(`   • Environment: ${process.env.NODE_ENV || 'development'}`);

      // Enable garbage collection monitoring
      if (global.gc) {
        this.gcEnabled = true;
        this.setupGCMonitoring();
      }

      // Setup memory monitoring
      this.setupMemoryMonitoring();

      // Setup memory cleanup intervals
      this.setupMemoryCleanup();
    } else {
      console.log('🔧 Standard memory configuration');
      console.log(`   • Memory limit: ${this.maxMemoryMB}MB`);
    }
  }

  setupGCMonitoring() {
    // Force garbage collection every 2 minutes in production
    setInterval(() => {
      if (global.gc) {
        const beforeGC = process.memoryUsage();
        global.gc();
        const afterGC = process.memoryUsage();
        
        const beforeMB = Math.round(beforeGC.heapUsed / 1024 / 1024);
        const afterMB = Math.round(afterGC.heapUsed / 1024 / 1024);
        const freedMB = beforeMB - afterMB;
        
        if (freedMB > 10) { // Only log if significant memory was freed
          console.log(`🗑️  GC: Freed ${freedMB}MB (${beforeMB}MB → ${afterMB}MB)`);
        }
      }
    }, 2 * 60 * 1000); // Every 2 minutes
  }

  setupMemoryMonitoring() {
    // Monitor memory usage every 30 seconds
    setInterval(() => {
      const usage = process.memoryUsage();
      const heapUsedMB = Math.round(usage.heapUsed / 1024 / 1024);
      const heapTotalMB = Math.round(usage.heapTotal / 1024 / 1024);
      const usagePercent = Math.round((usage.heapUsed / usage.heapTotal) * 100);
      
      // Log memory warnings for production-level environments
      if (this.isProductionLevel) {
        const warningThreshold = Math.floor(this.maxMemoryMB * 0.8); // 80% of max memory
        const emergencyThreshold = Math.floor(this.maxMemoryMB * 0.9); // 90% of max memory

        if (heapUsedMB > warningThreshold) {
          console.warn(`⚠️  High memory usage: ${heapUsedMB}MB/${heapTotalMB}MB (${usagePercent}%) - Threshold: ${warningThreshold}MB`);

          // Force GC if memory is very high
          if (global.gc && heapUsedMB > emergencyThreshold) {
            global.gc();
            console.log('🚨 Emergency garbage collection triggered');
          }
        }
      }
      
      // Save memory stats for monitoring
      this.saveMemoryStats({
        timestamp: new Date().toISOString(),
        heapUsed: heapUsedMB,
        heapTotal: heapTotalMB,
        usagePercent,
        rss: Math.round(usage.rss / 1024 / 1024),
        external: Math.round(usage.external / 1024 / 1024)
      });
    }, 30 * 1000); // Every 30 seconds
  }

  setupMemoryCleanup() {
    // Cleanup intervals for production-level environments
    if (this.isProductionLevel) {
      // Clear require cache for non-essential modules every 10 minutes
      setInterval(() => {
        this.cleanupRequireCache();
      }, 10 * 60 * 1000); // Every 10 minutes

      // Clear temporary data every 5 minutes
      setInterval(() => {
        this.cleanupTemporaryData();
      }, 5 * 60 * 1000); // Every 5 minutes
    }
  }

  cleanupRequireCache() {
    let cleaned = 0;
    Object.keys(require.cache).forEach(key => {
      // Only clean non-essential modules (not node_modules or core files)
      if (!key.includes('node_modules') && 
          !key.includes('server/index.js') &&
          !key.includes('prisma') &&
          key.includes('temp') || key.includes('cache')) {
        delete require.cache[key];
        cleaned++;
      }
    });
    
    if (cleaned > 0) {
      console.log(`🧹 Cleaned ${cleaned} cached modules`);
    }
  }

  cleanupTemporaryData() {
    // Clear any temporary data structures
    // This is application-specific cleanup
    try {
      // Example: Clear temporary upload files older than 1 hour
      const tempDir = path.join(__dirname, '../temp');
      if (fs.existsSync(tempDir)) {
        const files = fs.readdirSync(tempDir);
        const oneHourAgo = Date.now() - (60 * 60 * 1000);
        
        files.forEach(file => {
          const filePath = path.join(tempDir, file);
          const stats = fs.statSync(filePath);
          
          if (stats.mtime.getTime() < oneHourAgo) {
            fs.unlinkSync(filePath);
          }
        });
      }
    } catch (error) {
      // Ignore cleanup errors
    }
  }

  saveMemoryStats(stats) {
    try {
      const statsFile = path.join(__dirname, '../logs/memory-stats.json');
      const dir = path.dirname(statsFile);
      
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
      
      let existingStats = [];
      if (fs.existsSync(statsFile)) {
        const data = fs.readFileSync(statsFile, 'utf8');
        existingStats = JSON.parse(data);
      }
      
      // Keep only last 100 entries
      existingStats.push(stats);
      if (existingStats.length > 100) {
        existingStats = existingStats.slice(-100);
      }
      
      fs.writeFileSync(statsFile, JSON.stringify(existingStats, null, 2));
    } catch (error) {
      // Ignore stats saving errors
    }
  }

  getMemoryStats() {
    const usage = process.memoryUsage();
    return {
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      rss: Math.round(usage.rss / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024),
      usagePercent: Math.round((usage.heapUsed / usage.heapTotal) * 100),
      maxMemoryMB: this.maxMemoryMB,
      isProduction: this.isProduction,
      isProductionLevel: this.isProductionLevel,
      treatDevAsProduction: this.treatDevAsProduction,
      gcEnabled: this.gcEnabled
    };
  }
}

// Initialize memory configuration
const memoryConfig = new ProductionMemoryConfig();

module.exports = memoryConfig;
