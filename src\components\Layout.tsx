import React, { useState } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  HomeIcon,
  BookOpenIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  UserGroupIcon,
  TagIcon,
  GiftIcon,
  ClockIcon,
  CloudArrowUpIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon,
  UserCircleIcon,
  ShieldCheckIcon,
  EnvelopeIcon,
  BuildingOfficeIcon,
  LanguageIcon,
  AcademicCapIcon,
  MapPinIcon,
  WrenchScrewdriverIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { useAuthStore } from '../store/authStore'
import { usePermissions, PermissionCheck } from '../hooks/usePermissions'
import Maithi<PERSON>V<PERSON>s<PERSON>oshLogo from './MaithiliVikasKoshLogo'

interface LayoutProps {
  children: React.ReactNode
}

const navigationSections = [
  {
    title: 'Main',
    items: [
      { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    ]
  },
  {
    title: 'Library Management',
    items: [
      { name: 'Books', href: '/books', icon: BookOpenIcon, permission: 'canManageBooks' },
      { name: 'Members', href: '/members', icon: UsersIcon, permission: 'canManageMembers' },
      { name: 'Borrowings', href: '/borrowings', icon: ClipboardDocumentListIcon, permission: 'canManageBorrowings' },
    ]
  },
  {
    title: 'Catalog',
    items: [
      { name: 'Authors', href: '/authors', icon: UserGroupIcon, permission: 'canManageCategories' },
      { name: 'Categories', href: '/categories', icon: TagIcon, permission: 'canManageCategories' },
      { name: 'Publishers', href: '/publishers', icon: BuildingOfficeIcon, permission: 'canManageCategories' },
      { name: 'Languages', href: '/languages', icon: LanguageIcon, permission: 'canManageCategories' },
      { name: 'Book Series', href: '/book-series', icon: BookOpenIcon, permission: 'canManageCategories' },
      { name: 'Subjects', href: '/subjects', icon: AcademicCapIcon, permission: 'canManageCategories' },
      { name: 'Locations', href: '/locations', icon: MapPinIcon, permission: 'canManageCategories' },
      { name: 'Conditions', href: '/conditions', icon: WrenchScrewdriverIcon, permission: 'canManageCategories' },
      { name: 'Sources', href: '/sources', icon: GiftIcon, permission: 'canManageSources' },
    ]
  },
  {
    title: 'Administration',
    items: [
      { name: 'User Management', href: '/user-management', icon: ShieldCheckIcon, permission: 'canManageUsers' },
      { name: 'Email Management', href: '/email-management', icon: EnvelopeIcon, permission: 'canManageSystem' },
      { name: 'Backup & Restore', href: '/backup-restore', icon: CloudArrowUpIcon, permission: 'canManageSystem' },
      { name: 'Soft Delete Management', href: '/soft-delete-management', icon: TrashIcon, role: 'ADMIN' },
      { name: 'User Logs', href: '/user-logs', icon: ClockIcon, permission: 'canViewLogs' },
    ]
  }
]

export default function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const location = useLocation()
  const { user, logout } = useAuthStore()
  const permissions = usePermissions()

  const handleLogout = () => {
    logout()
  }

  return (
    <div className="h-screen overflow-hidden windows-app-background">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 sidebar-mobile-overlay" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col sidebar-windows shadow-xl">
          <div className="flex h-16 items-center justify-between px-4 sidebar-header-windows">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MaithiliVikasKoshLogo
                  className="w-8 h-8 rounded-md object-contain"
                  width={32}
                  height={32}
                />
              </div>
              <div className="ml-3">
                <h1 className="text-lg font-semibold text-gray-900">विद्यापति पुस्तकालय</h1>
                <p className="text-xs text-gray-600">मैथिली विकास कोष</p>
              </div>
            </div>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-200 transition-colors"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>
          <nav className="flex-1 py-4 overflow-y-auto">
            {navigationSections.map((section, sectionIndex) => (
              <div key={section.title}>
                {sectionIndex > 0 && <div className="sidebar-section-divider" />}
                <div className="sidebar-section-title">{section.title}</div>
                <div className="space-y-1 mb-4">
                  {section.items.map((item) => {
                    // Check permission for navigation item
                    if (item.permission && !permissions[item.permission as keyof PermissionCheck]) {
                      return null
                    }

                    // Check role-based access
                    if (item.role && user?.role !== item.role) {
                      return null
                    }

                    const isActive = location.pathname === item.href || location.pathname.startsWith(item.href + '/')
                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        className={`sidebar-nav-item-windows text-gray-600 ${isActive ? 'active' : ''}`}
                        onClick={() => setSidebarOpen(false)}
                      >
                        <item.icon className="sidebar-nav-icon-windows" />
                        {item.name}
                      </Link>
                    )
                  })}
                </div>
              </div>
            ))}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col lg:z-30">
        <div className="flex flex-col h-full sidebar-windows">
          <div className="flex h-16 items-center px-6 sidebar-header-windows">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <MaithiliVikasKoshLogo
                  className="w-10 h-10 rounded-md object-contain"
                  width={40}
                  height={40}
                />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-700">मैथिली विकास कोष</p>
                <h1 className="text-lg font-semibold text-gray-900">विद्यापति पुस्तकालय</h1>
              </div>
            </div>
          </div>
          <nav className="flex-1 py-4 overflow-y-auto">
            {navigationSections.map((section, sectionIndex) => (
              <div key={section.title}>
                {sectionIndex > 0 && <div className="sidebar-section-divider" />}
                <div className="sidebar-section-title">{section.title}</div>
                <div className="space-y-1 mb-4">
                  {section.items.map((item) => {
                    // Check permission for navigation item
                    if (item.permission && !permissions[item.permission as keyof PermissionCheck]) {
                      return null
                    }

                    // Check role-based access
                    if (item.role && user?.role !== item.role) {
                      return null
                    }

                    const isActive = location.pathname === item.href || location.pathname.startsWith(item.href + '/')
                    return (
                      <Link
                        key={item.name}
                        to={item.href}
                        className={`sidebar-nav-item-windows text-gray-600 ${isActive ? 'active' : ''}`}
                      >
                        <item.icon className="sidebar-nav-icon-windows" />
                        {item.name}
                      </Link>
                    )
                  })}
                </div>
              </div>
            ))}
          </nav>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 ultrawide:pl-72 4k:pl-80 8k:pl-96 h-screen flex flex-col">
        {/* Windows-style Top navigation */}
        <div className="flex-shrink-0 z-40 flex h-16 border-b border-gray-300 windows-header">
          <button
            type="button"
            className="px-4 text-gray-500 hover:text-gray-700 rounded-md mx-2 my-3 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 lg:hidden windows-button"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="w-6 h-6" />
          </button>

          <div className="flex flex-1 justify-between px-4 lg:px-6">
            <div className="flex flex-1 items-center">
              <h2 className="text-lg font-semibold text-gray-800 capitalize">
                {location.pathname.split('/')[1] || 'Dashboard'}
              </h2>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3 px-3 py-2 windows-user-info">
                <UserCircleIcon className="w-6 h-6 text-gray-500" />
                <div className="text-sm">
                  <p className="font-medium text-gray-900">{user?.name}</p>
                  <p className="text-gray-600">@{user?.username}</p>
                </div>
              </div>

              <button
                onClick={handleLogout}
                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 windows-logout-button"
              >
                <ArrowRightOnRectangleIcon className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>

        {/* Windows-style Page content */}
        <main className="flex-1 overflow-y-auto windows-main-content flex flex-col">
          <div className="flex-1 py-6">
            <div className="responsive-container">
              {children}
            </div>
          </div>

          {/* Main Content Footer - Sticky to bottom */}
          <footer className="mt-auto border-t border-gray-200 bg-gray-50 px-4 py-4">
            <div className="responsive-container">
              <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-600">
                <div className="flex items-center space-x-4">
                  <span className="font-medium text-gray-700">
                    मैथिली विकास कोष - विद्यापति पुस्तकालय
                  </span>
                </div>
                <div className="flex items-center space-x-4 mt-2 sm:mt-0">
                  <span>Developed by <span className="font-semibold text-primary-600">श्रद्धेय वात्स्यायन</span></span>
                  <span className="text-gray-400">|</span>
                  <span>📞 9844361480</span>
                  <span className="text-gray-400">|</span>
                  <span>© 2025 All rights reserved.</span>
                </div>
              </div>
            </div>
          </footer>
        </main>
      </div>
    </div>
  )
}
