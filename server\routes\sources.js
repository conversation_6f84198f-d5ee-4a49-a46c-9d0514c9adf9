const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { extractUser, requireManageSources } = require('../middleware/auth');
const { logAction, ACTIONS } = require('../utils/logger');
const router = express.Router();
const prisma = new PrismaClient();

// Get all sources with pagination and search
router.get('/', extractUser, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      type = '',
      active = '',
      sortBy = 'name',
      sortOrder = 'asc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    const where = {
      isDeleted: false,
      ...(search && {
        OR: [
          { name: { contains: search } },
          { nameNepali: { contains: search } },
          { contactPerson: { contains: search } },
          { email: { contains: search } },
          { phone: { contains: search } },
          { description: { contains: search } }
        ]
      }),
      ...(type && { type }),
      ...(active !== '' && { isActive: active === 'true' })
    };

    const [sources, total] = await Promise.all([
      prisma.source.findMany({
        where,
        include: {
          books: {
            where: { isDeleted: false },
            select: { id: true, title: true, price: true, dateReceived: true }
          }
        },
        skip,
        take: parseInt(limit),
        orderBy: { [sortBy]: sortOrder }
      }),
      prisma.source.count({ where })
    ]);

    // Calculate statistics for each source
    const sourcesWithStats = sources.map(source => ({
      ...source,
      totalBooks: source.books.length,
      totalValue: source.books.reduce((sum, book) => sum + (book.price || 0), 0),
      firstDonation: source.books.length > 0
        ? source.books.reduce((earliest, book) =>
            !earliest || (book.dateReceived && new Date(book.dateReceived) < new Date(earliest))
              ? book.dateReceived : earliest, null)
        : null,
      lastDonation: source.books.length > 0
        ? source.books.reduce((latest, book) =>
            !latest || (book.dateReceived && new Date(book.dateReceived) > new Date(latest))
              ? book.dateReceived : latest, null)
        : null
    }));

    // Log search action if search term provided, otherwise log view action
    if (search) {
      await logAction(req, ACTIONS.SEARCH, 'SOURCE', null, `Search: "${search}" (${sourcesWithStats.length} results)`);
    } else {
      await logAction(req, ACTIONS.VIEW, 'SOURCE', null, `Viewed sources list (page ${page}, ${sourcesWithStats.length} sources)`);
    }

    res.json({
      sources: sourcesWithStats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (error) {
    console.error('Error fetching sources:', error);
    res.status(500).json({ error: 'Failed to fetch sources' });
  }
});

// Get source by ID
router.get('/:id', extractUser, async (req, res) => {
  try {
    const { id } = req.params;

    const source = await prisma.source.findFirst({
      where: { id, isDeleted: false },
      include: {
        books: {
          where: { isDeleted: false },
          include: {
            author: true,
            category: true,
            borrowings: {
              where: { status: 'ISSUED' },
              include: { member: true }
            }
          },
          orderBy: { dateReceived: 'desc' }
        }
      }
    });

    if (!source) {
      return res.status(404).json({ error: 'Source not found' });
    }

    // Calculate first and last donation dates from books
    const calculatedFirstDonation = source.books.length > 0
      ? source.books.reduce((earliest, book) =>
          !earliest || (book.dateReceived && new Date(book.dateReceived) < new Date(earliest))
            ? book.dateReceived : earliest, null)
      : null;

    const calculatedLastDonation = source.books.length > 0
      ? source.books.reduce((latest, book) =>
          !latest || (book.dateReceived && new Date(book.dateReceived) > new Date(latest))
            ? book.dateReceived : latest, null)
      : null;

    // Calculate comprehensive statistics
    const stats = {
      totalBooks: source.books.length,
      totalValue: source.books.reduce((sum, book) => sum + (book.price || 0), 0),
      availableBooks: source.books.filter(book => book.isAvailable).length,
      borrowedBooks: source.books.filter(book => !book.isAvailable).length,
      // Use calculated dates from books if available, otherwise fall back to manually set dates
      firstDonation: calculatedFirstDonation ? calculatedFirstDonation : (source.firstDonation ? source.firstDonation : null),
      lastDonation: calculatedLastDonation ? calculatedLastDonation : (source.lastDonation ? source.lastDonation : null),
      averageBookValue: source.books.length > 0
        ? source.books.reduce((sum, book) => sum + (book.price || 0), 0) / source.books.length
        : 0,
      booksByCategory: source.books.reduce((acc, book) => {
        const categoryName = book.category.name;
        acc[categoryName] = (acc[categoryName] || 0) + 1;
        return acc;
      }, {}),
      booksByLanguage: source.books.reduce((acc, book) => {
        acc[book.language] = (acc[book.language] || 0) + 1;
        return acc;
      }, {}),
      recentBooks: source.books.slice(0, 10) // Last 10 books
    };

    await logAction(req, ACTIONS.VIEW, 'SOURCE', id, `Viewed source: ${source.name}`);

    res.json({ ...source, stats });
  } catch (error) {
    console.error('Error fetching source:', error);
    res.status(500).json({ error: 'Failed to fetch source' });
  }
});

// Create new source
router.post('/', extractUser, requireManageSources, async (req, res) => {
  try {
    const sourceData = req.body;

    // Check if source name already exists
    const existingSource = await prisma.source.findFirst({
      where: {
        name: sourceData.name,
        isDeleted: false
      }
    });

    if (existingSource) {
      return res.status(400).json({ error: 'Source name already exists' });
    }

    const source = await prisma.source.create({
      data: {
        ...sourceData,
        totalValue: sourceData.totalValue ? parseFloat(sourceData.totalValue) : 0,
        firstDonation: sourceData.firstDonation ? new Date(sourceData.firstDonation) : null,
        lastDonation: sourceData.lastDonation ? new Date(sourceData.lastDonation) : null
      }
    });

    await logAction(req, ACTIONS.CREATE, 'SOURCE', source.id, `Created source: ${source.name}`);

    res.status(201).json(source);
  } catch (error) {
    console.error('Error creating source:', error);
    res.status(500).json({ error: 'Failed to create source' });
  }
});

// Update source
router.put('/:id', extractUser, requireManageSources, async (req, res) => {
  try {
    const { id } = req.params;
    const sourceData = req.body;

    // Check if source exists
    const existingSource = await prisma.source.findFirst({
      where: { id, isDeleted: false }
    });

    if (!existingSource) {
      return res.status(404).json({ error: 'Source not found' });
    }

    // Check if new name conflicts with existing source
    if (sourceData.name !== existingSource.name) {
      const nameConflict = await prisma.source.findFirst({
        where: {
          name: sourceData.name,
          isDeleted: false,
          id: { not: id }
        }
      });

      if (nameConflict) {
        return res.status(400).json({ error: 'Source name already exists' });
      }
    }

    const source = await prisma.source.update({
      where: { id },
      data: {
        ...sourceData,
        totalValue: sourceData.totalValue ? parseFloat(sourceData.totalValue) : existingSource.totalValue,
        firstDonation: sourceData.firstDonation ? new Date(sourceData.firstDonation) : existingSource.firstDonation,
        lastDonation: sourceData.lastDonation ? new Date(sourceData.lastDonation) : existingSource.lastDonation
      }
    });

    await logAction(req, ACTIONS.UPDATE, 'SOURCE', id, `Updated source: ${source.name}`);

    res.json(source);
  } catch (error) {
    console.error('Error updating source:', error);
    res.status(500).json({ error: 'Failed to update source' });
  }
});

// Delete source (soft delete)
router.delete('/:id', extractUser, requireManageSources, async (req, res) => {
  try {
    const { id } = req.params;

    const source = await prisma.source.findFirst({
      where: { id, isDeleted: false },
      include: {
        books: { where: { isDeleted: false } }
      }
    });

    if (!source) {
      return res.status(404).json({ error: 'Source not found' });
    }

    // Check if source has books
    if (source.books.length > 0) {
      return res.status(400).json({
        error: `Cannot delete source. It has ${source.books.length} books associated with it.`
      });
    }

    await prisma.source.update({
      where: { id },
      data: { isDeleted: true }
    });

    await logAction(req, ACTIONS.DELETE, 'SOURCE', id, `Deleted source: ${source.name}`);

    res.json({ message: 'Source deleted successfully' });
  } catch (error) {
    console.error('Error deleting source:', error);
    res.status(500).json({ error: 'Failed to delete source' });
  }
});

// Get books by source with pagination
router.get('/:id/books', extractUser, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = '',
      sortBy = 'dateReceived',
      sortOrder = 'desc'
    } = req.query;

    // Verify source exists
    const source = await prisma.source.findUnique({
      where: { id: req.params.id, isDeleted: false }
    });

    if (!source) {
      return res.status(404).json({ error: 'Source not found' });
    }

    const where = {
      sourceId: req.params.id,
      isDeleted: false,
      ...(search && {
        OR: [
          { title: { contains: search } },
          { titleNepali: { contains: search } },
          { accessionNo: { contains: search } },
          { author: { name: { contains: search } } },
          { author: { nameNepali: { contains: search } } },
          { category: { name: { contains: search } } },
          { category: { nameNepali: { contains: search } } }
        ]
      })
    };

    // Get total count for pagination
    const total = await prisma.book.count({ where });

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;
    const pages = Math.ceil(total / limitNum);

    const books = await prisma.book.findMany({
      where,
      include: {
        author: {
          select: { name: true, nameNepali: true }
        },
        category: {
          select: { name: true, nameNepali: true }
        },
        language: {
          select: { name: true, nameNepali: true }
        },
        borrowings: {
          where: { status: 'ISSUED' },
          include: {
            member: {
              select: { name: true, memberNo: true }
            }
          }
        }
      },
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limitNum
    });

    // Log view action
    await logAction(req, ACTIONS.VIEW, 'SOURCE', req.params.id, `Viewed books by source (${books.length} books)`);

    res.json({
      books,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages
      }
    });
  } catch (error) {
    console.error('Error fetching books by source:', error);
    res.status(500).json({ error: 'Failed to fetch books by source' });
  }
});

// Get source statistics
router.get('/:id/statistics', extractUser, async (req, res) => {
  try {
    const { id } = req.params;

    const source = await prisma.source.findFirst({
      where: { id, isDeleted: false },
      include: {
        books: {
          where: { isDeleted: false },
          include: {
            category: true,
            borrowings: true
          }
        }
      }
    });

    if (!source) {
      return res.status(404).json({ error: 'Source not found' });
    }

    const stats = {
      totalBooks: source.books.length,
      totalValue: source.books.reduce((sum, book) => sum + (book.price || 0), 0),
      availableBooks: source.books.filter(book => book.isAvailable).length,
      borrowedBooks: source.books.filter(book => !book.isAvailable).length,
      totalBorrowings: source.books.reduce((sum, book) => sum + book.borrowings.length, 0),
      activeBorrowings: source.books.reduce((sum, book) =>
        sum + book.borrowings.filter(b => b.status === 'ISSUED').length, 0),
      booksByCategory: source.books.reduce((acc, book) => {
        const categoryName = book.category.name;
        acc[categoryName] = (acc[categoryName] || 0) + 1;
        return acc;
      }, {}),
      booksByLanguage: source.books.reduce((acc, book) => {
        acc[book.language] = (acc[book.language] || 0) + 1;
        return acc;
      }, {}),
      booksByYear: source.books.reduce((acc, book) => {
        const year = book.dateReceived ? new Date(book.dateReceived).getFullYear() : 'Unknown';
        acc[year] = (acc[year] || 0) + 1;
        return acc;
      }, {})
    };

    res.json(stats);
  } catch (error) {
    console.error('Error fetching source statistics:', error);
    res.status(500).json({ error: 'Failed to fetch source statistics' });
  }
});

// Merge two sources
router.post('/merge', extractUser, requireManageSources, async (req, res) => {
  try {
    const { primarySourceId, secondarySourceId, mergeData } = req.body;

    if (!primarySourceId || !secondarySourceId) {
      return res.status(400).json({ error: 'Both primary and secondary source IDs are required' });
    }

    if (primarySourceId === secondarySourceId) {
      return res.status(400).json({ error: 'Cannot merge a source with itself' });
    }

    // Start transaction
    const result = await prisma.$transaction(async (tx) => {
      // Get both sources
      const [primarySource, secondarySource] = await Promise.all([
        tx.source.findFirst({
          where: { id: primarySourceId, isDeleted: false },
          include: { books: { where: { isDeleted: false } } }
        }),
        tx.source.findFirst({
          where: { id: secondarySourceId, isDeleted: false },
          include: { books: { where: { isDeleted: false } } }
        })
      ]);

      if (!primarySource || !secondarySource) {
        throw new Error('One or both sources not found');
      }

      // Update all books from secondary source to primary source
      await tx.book.updateMany({
        where: { sourceId: secondarySourceId, isDeleted: false },
        data: { sourceId: primarySourceId }
      });

      // Calculate merged statistics
      const totalBooks = primarySource.books.length + secondarySource.books.length;
      const totalValue = primarySource.books.reduce((sum, book) => sum + (book.price || 0), 0) +
                        secondarySource.books.reduce((sum, book) => sum + (book.price || 0), 0);

      const firstDonation = [primarySource.firstDonation, secondarySource.firstDonation]
        .filter(Boolean)
        .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())[0];

      const lastDonation = [primarySource.lastDonation, secondarySource.lastDonation]
        .filter(Boolean)
        .sort((a, b) => new Date(b).getTime() - new Date(a).getTime())[0];

      // Update primary source with merged data and statistics
      const updatedPrimarySource = await tx.source.update({
        where: { id: primarySourceId },
        data: {
          // Use provided merge data or keep primary source data
          name: mergeData.name || primarySource.name,
          nameNepali: mergeData.nameNepali || primarySource.nameNepali || secondarySource.nameNepali,
          type: mergeData.type || primarySource.type,
          contactPerson: mergeData.contactPerson || primarySource.contactPerson || secondarySource.contactPerson,
          email: mergeData.email || primarySource.email || secondarySource.email,
          phone: mergeData.phone || primarySource.phone || secondarySource.phone,
          address: mergeData.address || primarySource.address || secondarySource.address,
          description: mergeData.description ||
                      (primarySource.description && secondarySource.description
                        ? `${primarySource.description} | Merged from: ${secondarySource.description}`
                        : primarySource.description || secondarySource.description),
          // Update statistics
          totalBooks: totalBooks,
          totalValue: totalValue,
          firstDonation: firstDonation,
          lastDonation: lastDonation
        }
      });

      // Soft delete the secondary source
      await tx.source.update({
        where: { id: secondarySourceId },
        data: {
          isDeleted: true,
          description: `${secondarySource.description || ''} | MERGED INTO: ${primarySource.name} on ${new Date().toISOString()}`
        }
      });

      return {
        mergedSource: updatedPrimarySource,
        mergedBooksCount: secondarySource.books.length,
        deletedSource: secondarySource
      };
    });

    // Log the merge action
    await logAction(req, ACTIONS.UPDATE, 'SOURCE', primarySourceId,
      `Merged source "${result.deletedSource.name}" into "${result.mergedSource.name}" (${result.mergedBooksCount} books transferred)`);

    res.json({
      message: 'Sources merged successfully',
      mergedSource: result.mergedSource,
      transferredBooks: result.mergedBooksCount,
      deletedSourceName: result.deletedSource.name
    });

  } catch (error) {
    console.error('Error merging sources:', error);
    res.status(500).json({ error: error.message || 'Failed to merge sources' });
  }
});

module.exports = router;
