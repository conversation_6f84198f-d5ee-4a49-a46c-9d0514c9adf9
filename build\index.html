<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>मैथिली विकास कोष - विद्यापति पुस्तकालय</title>
    <meta name="description" content="Industry-level Library Management System for Vidyapati Library by <PERSON><PERSON><PERSON><PERSON>" />

    <!-- Content Security Policy is now set via HTTP headers for better Electron compatibility -->

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
      /* Loading screen styles */
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        transition: opacity 0.5s ease-out;
      }

      .loading-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loading-logo {
        width: 80px;
        height: 80px;
        background: #475569;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 24px;
        box-shadow: 0 10px 40px -10px rgba(71, 85, 105, 0.3);
      }

      .loading-text {
        font-family: 'Inter', sans-serif;
        font-size: 24px;
        font-weight: 600;
        color: #334155;
        margin-bottom: 8px;
      }

      .loading-subtitle {
        font-family: 'Noto Sans Devanagari', sans-serif;
        font-size: 16px;
        color: #64748b;
        margin-bottom: 32px;
      }

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e2e8f0;
        border-top: 3px solid #475569;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
    <script type="module" crossorigin src="./assets/index-CGGyY4kX.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vendor-BtP0CW_r.js">
    <link rel="modulepreload" crossorigin href="./assets/router-CxVBsyN8.js">
    <link rel="modulepreload" crossorigin href="./assets/utils-Bg8zLjK0.js">
    <link rel="stylesheet" crossorigin href="./assets/index-LiL-FArY.css">
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
      <div class="loading-logo">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4 19.5A2.5 2.5 0 0 1 1.5 17V7A2.5 2.5 0 0 1 4 4.5h16A2.5 2.5 0 0 1 22.5 7v10a2.5 2.5 0 0 1-2.5 2.5H4z" stroke="white" stroke-width="2"/>
          <path d="M9 9h6M9 13h6M9 17h4" stroke="white" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>
      <div class="loading-text">मैथिली विकास कोष</div>
      <div class="loading-subtitle">विद्यापति पुस्तकालय</div>
      <div class="loading-spinner"></div>
    </div>

    <div id="root"></div>

    <script>
      // Hide loading screen when app is ready
      window.addEventListener('load', () => {
        setTimeout(() => {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.classList.add('hidden');
            setTimeout(() => {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
